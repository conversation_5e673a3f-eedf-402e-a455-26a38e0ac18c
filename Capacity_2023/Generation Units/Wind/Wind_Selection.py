#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Wind_Selection.py

读取Global Wind Power Tracker数据，筛选2023年状态为operating的机组，
分为海上风和陆上风两种类型，统计202个国家的总容量。

Author: Generated for Global Power System Planning
Date: 2024
"""

import pandas as pd
import numpy as np
import os
from typing import Dict, Tuple

def load_country_mapping(filepath: str) -> Dict[str, str]:
    """
    加载国家名称到ISO代码的映射
    
    Parameters:
    -----------
    filepath : str
        Capacity_2023.xlsx文件路径
    
    Returns:
    --------
    dict
        国家名称到ISO代码的映射字典
    """
    capacity_df = pd.read_excel(filepath)
    country_mapping = dict(zip(capacity_df['Country'], capacity_df['ISO_Code']))
    return country_mapping

def create_country_name_mapping() -> Dict[str, str]:
    """
    创建风电数据中的国家名到标准国家名的映射
    
    Returns:
    --------
    dict
        名称映射字典
    """
    name_mapping = {
        # 主要的名称不匹配问题
        'United States': 'United States of America',
        'South Korea': 'Korea, Republic of',
        'Türkiye': 'Turkey',
        'Russia': 'Russian Federation',
        'Iran': 'Iran, Islamic Republic of',
        'Venezuela': 'Venezuela, Bolivarian Republic of',
        'Syria': 'Syrian Arab Republic',
        'North Korea': 'Korea, Democratic People\'s Republic of',
        'Bolivia': 'Bolivia, Plurinational State of',
        'Moldova': 'Moldova, Republic of',
        'Tanzania': 'Tanzania, United Republic of',
        'Laos': 'Lao People\'s Democratic Republic',
        'Macedonia': 'North Macedonia',
        'Congo': 'Congo',
        'Democratic Republic of the Congo': 'Congo, Democratic Republic of the',
        'Ivory Coast': 'Côte d\'Ivoire',
        'Czechia': 'Czech Republic',
        'Eswatini': 'Eswatini',
        'Micronesia': 'Micronesia, Federated States of',
        'Palestine': 'Palestine, State of',
        'Brunei': 'Brunei Darussalam',
        'Cape Verde': 'Cabo Verde',
        'Saint Kitts and Nevis': 'Saint Kitts and Nevis',
        'Saint Lucia': 'Saint Lucia',
        'Saint Vincent and the Grenadines': 'Saint Vincent and the Grenadines',
        'São Tomé and Príncipe': 'Sao Tome and Principe',
        'Vatican': 'Holy See',
        
        # 处理一些特殊地区（这些不会在最终202国家列表中，但需要处理）
        'Taiwan': 'Taiwan, Province of China',
        'Puerto Rico': 'United States of America',  # 美国领土
        'Guam': 'United States of America',         # 美国领土
        'American Samoa': 'United States of America',  # 美国领土
        'Faroe Islands': 'Denmark',     # 丹麦领土
        'Greenland': 'Denmark',         # 丹麦领土
        'Aruba': 'Netherlands',         # 荷兰领土
        'Curaçao': 'Netherlands',       # 荷兰领土
        'Bonaire, Sint Eustatius, and Saba': 'Netherlands',  # 荷兰领土
        'New Caledonia': 'France',      # 法国领土
        'Guadeloupe': 'France',         # 法国领土
        'Martinique': 'France',         # 法国领土
        'Réunion': 'France',            # 法国领土
        'Bermuda': 'United Kingdom',    # 英国领土
        'Guernsey': 'United Kingdom',   # 英国王室领地
        'Jersey': 'United Kingdom',     # 英国王室领地
        'Isle of Man': 'United Kingdom', # 英国王室领地
        'Åland Islands': 'Finland',     # 芬兰自治区
        'Christmas Island': 'Australia', # 澳大利亚领土
    }
    return name_mapping

def filter_operating_projects_2023(wind_df: pd.DataFrame) -> pd.DataFrame:
    """
    筛选2023年运营中的风电项目
    
    Parameters:
    -----------
    wind_df : pd.DataFrame
        风电项目数据
    
    Returns:
    --------
    pd.DataFrame
        筛选后的数据
    """
    # 筛选条件：
    # 1. Status == 'operating'
    # 2. Start year <= 2023
    # 3. Retired year >= 2023 或者 Retired year 为空（未退役）
    
    operating_df = wind_df[wind_df['Status'] == 'operating'].copy()
    
    # Start year <= 2023
    operating_df = operating_df[operating_df['Start year'] <= 2023]
    
    # Retired year >= 2023 或者为空
    retired_condition = (operating_df['Retired year'].isna()) | (operating_df['Retired year'] >= 2023)
    operating_df = operating_df[retired_condition]
    
    return operating_df

def classify_installation_type(installation_type: str) -> str:
    """
    将installation type分类为Onshore或Offshore
    
    Parameters:
    -----------
    installation_type : str
        原始安装类型
    
    Returns:
    --------
    str
        'Onshore' 或 'Offshore'
    """
    if pd.isna(installation_type):
        return 'Onshore'  # Unknown归为Onshore
    
    installation_type = str(installation_type).lower()
    
    if 'offshore' in installation_type:
        return 'Offshore'
    elif installation_type == 'unknown':
        return 'Onshore'  # Unknown类型归为Onshore
    else:
        return 'Onshore'

def aggregate_wind_capacity_by_country(wind_df: pd.DataFrame, 
                                     country_mapping: Dict[str, str],
                                     name_mapping: Dict[str, str]) -> pd.DataFrame:
    """
    按国家和类型聚合风电容量
    
    Parameters:
    -----------
    wind_df : pd.DataFrame
        筛选后的风电数据
    country_mapping : dict
        国家名到ISO代码的映射
    name_mapping : dict
        风电数据中国家名到标准国家名的映射
    
    Returns:
    --------
    pd.DataFrame
        按国家聚合的容量数据
    """
    # 创建工作副本
    df = wind_df.copy()
    
    # 应用installation type分类
    df['Wind_Type'] = df['Installation Type'].apply(classify_installation_type)
    
    # 标准化国家名称
    df['Country_Standard'] = df['Country/Area'].map(name_mapping).fillna(df['Country/Area'])
    
    # 只保留在202国家映射表中的国家
    df = df[df['Country_Standard'].isin(country_mapping.keys())]
    
    # 添加ISO代码
    df['ISO_Code'] = df['Country_Standard'].map(country_mapping)
    
    # 按国家和风电类型聚合容量
    aggregated = df.groupby(['ISO_Code', 'Country_Standard', 'Wind_Type'])['Capacity (MW)'].sum().reset_index()
    
    # 透视表，将Onshore和Offshore分开
    pivot_df = aggregated.pivot_table(
        index=['ISO_Code', 'Country_Standard'], 
        columns='Wind_Type', 
        values='Capacity (MW)', 
        fill_value=0
    ).reset_index()
    
    # 重命名列
    pivot_df.columns.name = None
    if 'Offshore' not in pivot_df.columns:
        pivot_df['Offshore'] = 0
    if 'Onshore' not in pivot_df.columns:
        pivot_df['Onshore'] = 0
    
    # 计算总容量
    pivot_df['Total_Wind_Capacity'] = pivot_df['Onshore'] + pivot_df['Offshore']
    
    # 重新排列列的顺序
    pivot_df = pivot_df[['ISO_Code', 'Country_Standard', 'Onshore', 'Offshore', 'Total_Wind_Capacity']]
    
    return pivot_df

def ensure_all_countries_included(aggregated_df: pd.DataFrame, 
                                country_mapping: Dict[str, str]) -> pd.DataFrame:
    """
    确保所有202个国家都包含在结果中（没有风电的国家容量为0）
    
    Parameters:
    -----------
    aggregated_df : pd.DataFrame
        聚合后的数据
    country_mapping : dict
        完整的国家映射
    
    Returns:
    --------
    pd.DataFrame
        包含所有202个国家的完整数据
    """
    # 创建完整的国家列表
    all_countries = pd.DataFrame({
        'Country_Standard': list(country_mapping.keys()),
        'ISO_Code': list(country_mapping.values())
    })
    
    # 合并数据，没有风电数据的国家用0填充
    complete_df = all_countries.merge(aggregated_df, on=['ISO_Code', 'Country_Standard'], how='left')
    complete_df[['Onshore', 'Offshore', 'Total_Wind_Capacity']] = complete_df[['Onshore', 'Offshore', 'Total_Wind_Capacity']].fillna(0)
    
    # 按总容量排序
    complete_df = complete_df.sort_values('Total_Wind_Capacity', ascending=False)
    
    return complete_df

def main():
    """主函数"""
    # 文件路径
    wind_file = 'Capacity_2023/Wind/Global-Wind-Power-Tracker-June-2024.xlsx'
    capacity_file = 'Capacity_2023/Capacity_2023.xlsx'
    output_file = 'Wind_Capacity_2023_by_Country.xlsx'
    
    print("开始处理风电数据...")
    
    # 1. 加载数据
    print("1. 加载数据...")
    if not os.path.exists(wind_file):
        raise FileNotFoundError(f"找不到风电数据文件: {wind_file}")
    if not os.path.exists(capacity_file):
        raise FileNotFoundError(f"找不到国家映射文件: {capacity_file}")
    
    # 加载主要数据表和Below Threshold表
    wind_df_main = pd.read_excel(wind_file, sheet_name='Data')
    wind_df_below = pd.read_excel(wind_file, sheet_name='Below Threshold')
    
    # 合并两个数据表
    wind_df_main['Data_Source'] = 'Main_Data'
    wind_df_below['Data_Source'] = 'Below_Threshold'
    wind_df = pd.concat([wind_df_main, wind_df_below], ignore_index=True)
    
    country_mapping = load_country_mapping(capacity_file)
    name_mapping = create_country_name_mapping()
    
    print(f"   - 加载主要风电项目数据: {len(wind_df_main)} 个项目")
    print(f"   - 加载Below Threshold数据: {len(wind_df_below)} 个项目")
    print(f"   - 合并后总项目数: {len(wind_df)} 个项目")
    print(f"   - 加载国家映射: {len(country_mapping)} 个国家")
    
    # 2. 筛选2023年运营中的项目
    print("2. 筛选2023年运营中的项目...")
    operating_df = filter_operating_projects_2023(wind_df)
    print(f"   - 2023年运营中的项目: {len(operating_df)} 个")
    print(f"   - 总容量: {operating_df['Capacity (MW)'].sum():.1f} MW")
    
    # 3. 按国家和类型聚合
    print("3. 按国家和类型聚合容量...")
    aggregated_df = aggregate_wind_capacity_by_country(operating_df, country_mapping, name_mapping)
    print(f"   - 有风电数据的国家: {len(aggregated_df)} 个")
    
    # 4. 确保包含所有202个国家
    print("4. 生成完整的202国家数据...")
    complete_df = ensure_all_countries_included(aggregated_df, country_mapping)
    
    # 5. 生成统计信息
    print("\n=== 风电容量统计 (2023年) ===")
    total_onshore = complete_df['Onshore'].sum()
    total_offshore = complete_df['Offshore'].sum()
    total_wind = complete_df['Total_Wind_Capacity'].sum()
    countries_with_wind = len(complete_df[complete_df['Total_Wind_Capacity'] > 0])
    
    # 按数据来源统计
    main_data_projects = len(operating_df[operating_df['Data_Source'] == 'Main_Data'])
    below_threshold_projects = len(operating_df[operating_df['Data_Source'] == 'Below_Threshold'])
    main_data_capacity = operating_df[operating_df['Data_Source'] == 'Main_Data']['Capacity (MW)'].sum()
    below_threshold_capacity = operating_df[operating_df['Data_Source'] == 'Below_Threshold']['Capacity (MW)'].sum()
    
    print(f"陆上风电总容量: {total_onshore:,.1f} MW")
    print(f"海上风电总容量: {total_offshore:,.1f} MW")
    print(f"风电总容量: {total_wind:,.1f} MW")
    print(f"拥有风电的国家数量: {countries_with_wind} / 202")
    print(f"\n数据来源统计:")
    print(f"  主要数据表项目: {main_data_projects} 个，容量: {main_data_capacity:,.1f} MW")
    print(f"  Below Threshold项目: {below_threshold_projects} 个，容量: {below_threshold_capacity:,.1f} MW")
    
    # 显示前10个国家
    print(f"\n风电容量前10的国家:")
    top_10 = complete_df.head(10)
    for _, row in top_10.iterrows():
        print(f"  {row['ISO_Code']} - {row['Country_Standard']}: "
              f"{row['Total_Wind_Capacity']:,.1f} MW "
              f"(陆上: {row['Onshore']:,.1f}, 海上: {row['Offshore']:,.1f})")
    
    # 6. 保存结果
    print(f"\n5. 保存结果到 {output_file}...")
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 主要结果
        complete_df.to_excel(writer, sheet_name='Wind_Capacity_by_Country', index=False)
        
        # 统计摘要
        summary_df = pd.DataFrame({
            '类型': ['陆上风电', '海上风电', '风电总计'],
            '总容量 (MW)': [total_onshore, total_offshore, total_wind],
            '国家数量': [
                len(complete_df[complete_df['Onshore'] > 0]),
                len(complete_df[complete_df['Offshore'] > 0]),
                countries_with_wind
            ]
        })
        summary_df.to_excel(writer, sheet_name='Summary', index=False)
        
        # 原始筛选数据（用于验证）
        operating_df_export = operating_df[['Country/Area', 'Project Name', 'Capacity (MW)', 
                                          'Installation Type', 'Status', 'Start year', 'Retired year', 'Data_Source']].copy()
        operating_df_export.to_excel(writer, sheet_name='Operating_Projects_2023', index=False)
        
        # 按数据来源分别统计
        source_summary = operating_df.groupby('Data_Source').agg({
            'Capacity (MW)': ['count', 'sum', 'mean'],
            'Project Name': 'count'
        }).round(2)
        source_summary.columns = ['项目数量', '总容量(MW)', '平均容量(MW)', '项目计数']
        source_summary = source_summary[['项目数量', '总容量(MW)', '平均容量(MW)']]
        source_summary.to_excel(writer, sheet_name='Data_Source_Summary')
    
    print(f"处理完成！结果已保存到 {output_file}")
    print(f"\n文件包含以下工作表:")
    print(f"  - Wind_Capacity_by_Country: 202个国家的风电容量数据")
    print(f"  - Summary: 统计摘要")
    print(f"  - Operating_Projects_2023: 2023年运营中的项目详情")
    print(f"  - Data_Source_Summary: 按数据来源的统计摘要")

if __name__ == "__main__":
    main() 