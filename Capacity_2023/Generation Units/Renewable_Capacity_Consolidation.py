#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Renewable_Capacity_Consolidation.py

读取Solar、Hydro、Nuclear、Geothermal四个文件夹下的容量数据文件，
并补全为202个国家的容量列表。

Author: Generated for Global Power System Planning
Date: 2024
"""

import pandas as pd
import numpy as np
import os
from typing import Dict, Tuple

def load_country_mapping(filepath: str = 'Capacity_2023.xlsx') -> Tuple[Dict[str, str], pd.DataFrame]:
    """
    加载国家映射数据
    
    Parameters:
    -----------
    filepath : str
        国家映射文件路径
    
    Returns:
    --------
    tuple
        (ISO_Code到Country的映射字典, 完整的国家数据框)
    """
    country_df = pd.read_excel(filepath)
    iso_to_country = dict(zip(country_df['ISO_Code'], country_df['Country']))
    return iso_to_country, country_df

def load_solar_capacity() -> pd.DataFrame:
    """
    加载太阳能容量数据
    
    Returns:
    --------
    pd.DataFrame
        太阳能容量数据
    """
    try:
        solar_df = pd.read_excel('Solar/Solar_Capacity_Summary_2023.xlsx')
        solar_df = solar_df.rename(columns={'Total Capacity (MW)': 'Solar_Capacity_MW'})
        print(f"   - 太阳能数据: {len(solar_df)} 个国家")
        print(f"   - 太阳能总容量: {solar_df['Solar_Capacity_MW'].sum():,.1f} MW")
        return solar_df[['ISO_Code', 'Solar_Capacity_MW']]
    except Exception as e:
        print(f"读取太阳能数据出错: {e}")
        return pd.DataFrame(columns=['ISO_Code', 'Solar_Capacity_MW'])

def load_nuclear_capacity() -> pd.DataFrame:
    """
    加载核能容量数据
    
    Returns:
    --------
    pd.DataFrame
        核能容量数据
    """
    try:
        nuclear_df = pd.read_excel('Nuclear/Nuclear_Capacity_Summary_2023.xlsx')
        nuclear_df = nuclear_df.rename(columns={'Total Capacity (MW)': 'Nuclear_Capacity_MW'})
        print(f"   - 核能数据: {len(nuclear_df)} 个国家")
        print(f"   - 核能总容量: {nuclear_df['Nuclear_Capacity_MW'].sum():,.1f} MW")
        return nuclear_df[['ISO_Code', 'Nuclear_Capacity_MW']]
    except Exception as e:
        print(f"读取核能数据出错: {e}")
        return pd.DataFrame(columns=['ISO_Code', 'Nuclear_Capacity_MW'])

def load_geothermal_capacity() -> pd.DataFrame:
    """
    加载地热能容量数据
    
    Returns:
    --------
    pd.DataFrame
        地热能容量数据
    """
    try:
        geo_df = pd.read_excel('Geothermal/Geothermal_Capacity_Summary_2023.xlsx')
        geo_df = geo_df.rename(columns={'Total Capacity (MW)': 'Geothermal_Capacity_MW'})
        print(f"   - 地热能数据: {len(geo_df)} 个国家")
        print(f"   - 地热能总容量: {geo_df['Geothermal_Capacity_MW'].sum():,.1f} MW")
        return geo_df[['ISO_Code', 'Geothermal_Capacity_MW']]
    except Exception as e:
        print(f"读取地热能数据出错: {e}")
        return pd.DataFrame(columns=['ISO_Code', 'Geothermal_Capacity_MW'])

def load_hydro_capacity() -> pd.DataFrame:
    """
    加载水电容量数据（包括发电容量和抽蓄容量）
    
    Returns:
    --------
    pd.DataFrame
        水电容量数据
    """
    try:
        # 1. 加载发电容量数据
        hydro_gen_df = pd.read_excel('Hydro/Hydro_Generation_and_Capacity_With_Continent_Modified.xlsx')
        hydro_gen_df = hydro_gen_df[['ISO_Code', 'Total Capacity (MW)']].copy()
        hydro_gen_df = hydro_gen_df.rename(columns={'Total Capacity (MW)': 'Hydro_Generation_MW'})
        
        # 2. 加载抽蓄容量数据
        hydro_storage_df = pd.read_excel('Hydro/Hydro_Capacity_Summary_By_Country.xlsx')
        
        # 处理抽蓄数据 - 按国家汇总抽蓄容量
        if 'Category' in hydro_storage_df.columns and 'Total Capacity (MW)' in hydro_storage_df.columns:
            # 筛选储能相关数据：所有V2G类型都是双向充放电的储能设备（抽蓄）
            # 包括：V2G (Bidirectional Charge and Discharge) 和 V2G (Unknown Type - Marked)
            storage_mask = hydro_storage_df['Category'].str.contains('V2G', case=False, na=False)
            hydro_storage_filtered = hydro_storage_df[storage_mask]
            
            print(f"     筛选出储能数据: {len(hydro_storage_filtered)} 行")
            print(f"     储能类型分布: {hydro_storage_filtered['Category'].value_counts().to_dict()}")
            
            # 按国家汇总抽蓄容量
            hydro_storage_agg = hydro_storage_filtered.groupby('ISO_Code')['Total Capacity (MW)'].sum().reset_index()
            hydro_storage_agg = hydro_storage_agg.rename(columns={'Total Capacity (MW)': 'Hydro_Storage_MW'})
        else:
            # 如果没有Category列，直接汇总
            hydro_storage_agg = hydro_storage_df.groupby('ISO_Code')['Total Capacity (MW)'].sum().reset_index()
            hydro_storage_agg = hydro_storage_agg.rename(columns={'Total Capacity (MW)': 'Hydro_Storage_MW'})
        
        # 3. 合并发电容量和抽蓄容量
        hydro_combined = hydro_gen_df.merge(hydro_storage_agg, on='ISO_Code', how='outer')
        hydro_combined['Hydro_Generation_MW'] = hydro_combined['Hydro_Generation_MW'].fillna(0)
        hydro_combined['Hydro_Storage_MW'] = hydro_combined['Hydro_Storage_MW'].fillna(0)
        
        # 计算总的水电容量
        hydro_combined['Hydro_Total_MW'] = hydro_combined['Hydro_Generation_MW'] + hydro_combined['Hydro_Storage_MW']
        
        print(f"   - 水电发电数据: {len(hydro_gen_df)} 个国家")
        print(f"   - 水电抽蓄数据: {len(hydro_storage_agg)} 个国家")
        print(f"   - 水电发电总容量: {hydro_combined['Hydro_Generation_MW'].sum():,.1f} MW")
        print(f"   - 水电抽蓄总容量: {hydro_combined['Hydro_Storage_MW'].sum():,.1f} MW")
        print(f"   - 水电总容量: {hydro_combined['Hydro_Total_MW'].sum():,.1f} MW")
        
        return hydro_combined[['ISO_Code', 'Hydro_Generation_MW', 'Hydro_Storage_MW', 'Hydro_Total_MW']]
        
    except Exception as e:
        print(f"读取水电数据出错: {e}")
        return pd.DataFrame(columns=['ISO_Code', 'Hydro_Generation_MW', 'Hydro_Storage_MW', 'Hydro_Total_MW'])

def create_complete_capacity_dataset(iso_to_country: Dict[str, str], 
                                   solar_df: pd.DataFrame, 
                                   nuclear_df: pd.DataFrame, 
                                   geo_df: pd.DataFrame, 
                                   hydro_df: pd.DataFrame) -> pd.DataFrame:
    """
    创建包含所有202个国家的完整容量数据集
    
    Parameters:
    -----------
    iso_to_country : dict
        ISO代码到国家名称的映射
    solar_df, nuclear_df, geo_df, hydro_df : pd.DataFrame
        各种能源的容量数据
    
    Returns:
    --------
    pd.DataFrame
        完整的容量数据集
    """
    # 创建包含所有202个国家的基础数据框
    all_countries = pd.DataFrame({
        'ISO_Code': list(iso_to_country.keys()),
        'Country': list(iso_to_country.values())
    })
    
    # 逐步合并各种能源数据
    complete_df = all_countries.copy()
    
    # 合并太阳能数据
    complete_df = complete_df.merge(solar_df, on='ISO_Code', how='left')
    
    # 合并核能数据
    complete_df = complete_df.merge(nuclear_df, on='ISO_Code', how='left')
    
    # 合并地热能数据
    complete_df = complete_df.merge(geo_df, on='ISO_Code', how='left')
    
    # 合并水电数据
    complete_df = complete_df.merge(hydro_df, on='ISO_Code', how='left')
    
    # 填充缺失值为0
    capacity_columns = ['Solar_Capacity_MW', 'Nuclear_Capacity_MW', 'Geothermal_Capacity_MW', 
                       'Hydro_Generation_MW', 'Hydro_Storage_MW', 'Hydro_Total_MW']
    
    for col in capacity_columns:
        if col in complete_df.columns:
            complete_df[col] = complete_df[col].fillna(0)
    
    # 计算可再生能源总容量（不包括核能）
    complete_df['Renewable_Total_MW'] = (complete_df['Solar_Capacity_MW'] + 
                                        complete_df['Geothermal_Capacity_MW'] + 
                                        complete_df['Hydro_Total_MW'])
    
    # 计算所有清洁能源总容量（包括核能）
    complete_df['Clean_Energy_Total_MW'] = (complete_df['Renewable_Total_MW'] + 
                                          complete_df['Nuclear_Capacity_MW'])
    
    return complete_df

def generate_summary_statistics(complete_df: pd.DataFrame) -> pd.DataFrame:
    """
    生成汇总统计信息
    
    Parameters:
    -----------
    complete_df : pd.DataFrame
        完整的容量数据
    
    Returns:
    --------
    pd.DataFrame
        汇总统计信息
    """
    summary_data = []
    
    # 各种能源的统计
    energy_types = [
        ('太阳能', 'Solar_Capacity_MW'),
        ('核能', 'Nuclear_Capacity_MW'),
        ('地热能', 'Geothermal_Capacity_MW'),
        ('水电发电', 'Hydro_Generation_MW'),
        ('水电抽蓄', 'Hydro_Storage_MW'),
        ('水电总计', 'Hydro_Total_MW'),
        ('可再生能源总计', 'Renewable_Total_MW'),
        ('清洁能源总计', 'Clean_Energy_Total_MW')
    ]
    
    for name, column in energy_types:
        if column in complete_df.columns:
            total_capacity = complete_df[column].sum()
            countries_with_capacity = len(complete_df[complete_df[column] > 0])
            max_capacity = complete_df[column].max()
            max_country = complete_df.loc[complete_df[column].idxmax(), 'Country'] if max_capacity > 0 else 'N/A'
            
            summary_data.append({
                '能源类型': name,
                '总容量 (MW)': total_capacity,
                '拥有该能源的国家数量': countries_with_capacity,
                '最大容量 (MW)': max_capacity,
                '最大容量国家': max_country
            })
    
    return pd.DataFrame(summary_data)

def main():
    """主函数"""
    print("开始整合可再生能源容量数据...")
    
    # 1. 加载国家映射
    print("1. 加载国家映射数据...")
    iso_to_country, country_df = load_country_mapping()
    print(f"   - 国家映射: {len(iso_to_country)} 个国家")
    
    # 2. 加载各种能源数据
    print("\n2. 加载各种能源容量数据...")
    solar_df = load_solar_capacity()
    nuclear_df = load_nuclear_capacity()
    geo_df = load_geothermal_capacity()
    hydro_df = load_hydro_capacity()
    
    # 3. 创建完整数据集
    print("\n3. 创建完整的202国家容量数据集...")
    complete_df = create_complete_capacity_dataset(iso_to_country, solar_df, nuclear_df, geo_df, hydro_df)
    
    # 4. 生成统计信息
    print("\n4. 生成汇总统计...")
    summary_df = generate_summary_statistics(complete_df)
    
    # 5. 输出统计信息
    print("\n=== 可再生能源容量统计 (2023年) ===")
    for _, row in summary_df.iterrows():
        print(f"{row['能源类型']}: {row['总容量 (MW)']:,.1f} MW "
              f"(国家数: {row['拥有该能源的国家数量']}, "
              f"最大: {row['最大容量国家']} {row['最大容量 (MW)']:,.1f} MW)")
    
    # 6. 显示前10个清洁能源容量最大的国家
    print(f"\n清洁能源容量前10的国家:")
    top_10 = complete_df.nlargest(10, 'Clean_Energy_Total_MW')
    for _, row in top_10.iterrows():
        print(f"  {row['ISO_Code']} - {row['Country']}: {row['Clean_Energy_Total_MW']:,.1f} MW "
              f"(太阳能: {row['Solar_Capacity_MW']:,.1f}, 核能: {row['Nuclear_Capacity_MW']:,.1f}, "
              f"地热: {row['Geothermal_Capacity_MW']:,.1f}, 水电: {row['Hydro_Total_MW']:,.1f})")
    
    # 7. 保存结果
    output_file = 'Renewable_Energy_Capacity_2023_Complete.xlsx'
    print(f"\n5. 保存结果到 {output_file}...")
    
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 主要结果 - 完整的202国家数据
        complete_df.to_excel(writer, sheet_name='Complete_Capacity_Data', index=False)
        
        # 汇总统计
        summary_df.to_excel(writer, sheet_name='Summary_Statistics', index=False)
        
        # 各种能源的原始数据（用于验证）
        if not solar_df.empty:
            solar_df.to_excel(writer, sheet_name='Solar_Raw_Data', index=False)
        if not nuclear_df.empty:
            nuclear_df.to_excel(writer, sheet_name='Nuclear_Raw_Data', index=False)
        if not geo_df.empty:
            geo_df.to_excel(writer, sheet_name='Geothermal_Raw_Data', index=False)
        if not hydro_df.empty:
            hydro_df.to_excel(writer, sheet_name='Hydro_Raw_Data', index=False)
        
        # 只有可再生能源的国家（排除零值）
        renewable_only = complete_df[complete_df['Renewable_Total_MW'] > 0].copy()
        renewable_only = renewable_only.sort_values('Renewable_Total_MW', ascending=False)
        renewable_only.to_excel(writer, sheet_name='Countries_with_Renewables', index=False)
        
        # 只有核能的国家
        nuclear_only = complete_df[complete_df['Nuclear_Capacity_MW'] > 0].copy()
        nuclear_only = nuclear_only.sort_values('Nuclear_Capacity_MW', ascending=False)
        nuclear_only.to_excel(writer, sheet_name='Countries_with_Nuclear', index=False)
    
    print(f"处理完成！结果已保存到 {output_file}")
    print(f"\n文件包含以下工作表:")
    print(f"  - Complete_Capacity_Data: 202个国家的完整容量数据")
    print(f"  - Summary_Statistics: 汇总统计信息")
    print(f"  - Solar_Raw_Data: 太阳能原始数据")
    print(f"  - Nuclear_Raw_Data: 核能原始数据")
    print(f"  - Geothermal_Raw_Data: 地热能原始数据")
    print(f"  - Hydro_Raw_Data: 水电原始数据")
    print(f"  - Countries_with_Renewables: 拥有可再生能源的国家")
    print(f"  - Countries_with_Nuclear: 拥有核能的国家")

if __name__ == "__main__":
    main() 