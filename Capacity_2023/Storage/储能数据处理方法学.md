# 全球储能数据处理方法学

## 摘要

本文档详细描述了用于全球电力系统扩展规划模型的储能数据处理方法学。基于全球储能数据库(GESDB)，我们开发了一套完整的数据预处理、分类、参数估算和国家聚合流程，为202个国家提供了机械储能和电化学储能的装机容量数据。对于中国，对比抽蓄容量发现GESDB分析得到的机械储能（抽蓄+飞轮）仅为31399MW，与GEM统计的抽蓄51449MW有很大出入。<u>**且搜索google发现差不多在58000MW，发现GEM的数值比较接近，所以仅对CHN这一个国家的数据进行替换。**</u>另外2030年CHN抽蓄总规模达到120000MW，FYI。

![image-20250730152625722](/Users/<USER>/Library/Application Support/typora-user-images/image-20250730152625722.png)



## 1. 数据来源

### 1.1 原始数据
- **数据源**: Global Energy Storage Database (GESDB)
- **数据格式**: JSON格式，包含2,490个全球储能项目
- **数据字段**: 项目ID、地理位置、技术类型、功率容量、能量容量、运行状态等
- **数据时效**: 截至2023年的储能项目信息

### 1.2 国家标准化数据
- **参考标准**: ISO 3166-1 alpha-3国家代码
- **覆盖范围**: 202个国家和地区
- **数据来源**: `Capacity_2023_Clean.xlsx`文件中的国家-ISO代码映射表

## 2. 储能技术分类方法

### 2.1 分类原则
基于电力系统扩展规划模型的需求，将储能技术简化为两大类：

#### 2.1.1 机械储能系统 (Mechanical Energy Storage)
- **主要技术**: 抽水蓄能 (Pumped Hydro Storage, PHS)
- **关键词识别**: `pumped hydro`, `pumped storage`, `pumped-storage`, `phs`
- **技术特点**: 大容量、长时储能、低成本

#### 2.1.2 电化学储能系统 (Electrochemical Energy Storage)  
- **主要技术**: 锂离子电池等各类电池储能
- **关键词识别**: `battery`, `lithium`, `lead acid`, `sodium`, `flow battery`, `vanadium`, `electrochemical`, `nickel`, `zinc`
- **技术特点**: 响应快速、模块化部署、适合短时储能

### 2.2 排除条件
- **热储能**: 由于体量相对较小，在本研究中忽略不计
- **关键词**: `thermal`, `molten salt`, `phase change`, `sensible heat`

### 2.3 分类算法
```python
def classify_storage_new(tech_mid_type, tech_broad_category):
    # 1. 排除热储能
    if any(thermal_keyword in tech_type.lower()):
        return '热储能'  # 忽略
    
    # 2. 识别机械储能
    if any(mechanical_keyword in tech_type.lower()):
        return '机械储能'
    
    # 3. 识别电化学储能  
    if any(electrochemical_keyword in tech_type.lower()):
        return '电化学储能'
    
    # 4. 默认分类为电化学储能
    return '电化学储能'
```

## 3. 能量功率比计算方法

### 3.1 计算原理
能量功率比（Energy-to-Power Ratio, E/P Ratio）表示储能系统的持续放电时间，是储能技术的重要特征参数：

$$E/P = \frac{\text{Energy Capacity (kWh)}}{\text{Power Capacity (kW)}}$$

### 3.2 加权平均计算
为获得各储能类型的代表性E/P比值，采用功率容量加权平均方法：

$$\overline{(E/P)}_{\text{type}} = \frac{\sum_{i=1}^{n} (E/P)_i \times P_i}{\sum_{i=1}^{n} P_i}$$

其中：
- $(E/P)_i$: 第i个项目的能量功率比
- $P_i$: 第i个项目的功率容量
- $n$: 该储能类型下有完整E/P数据的项目数量

### 3.3 计算结果
基于GESDB数据计算得到的加权平均E/P比值：

| 储能类型 | 加权平均E/P比值 | 样本项目数 | 技术特征 |
|---------|---------------|----------|---------|
| 机械储能 | 38.18 小时 | 170 | 长时储能，适合日/周调节 |
| 电化学储能 | 2.12 小时 | 1,308 | 短时储能，适合分钟/小时调节 |

## 4. 缺失数据处理方法

### 4.1 问题识别
GESDB数据库中部分项目缺失能量容量信息，占总项目数的约31%。

### 4.2 估算方法
对于缺失能量容量的项目，采用以下估算公式：

$$E_{\text{estimated}} = P_{\text{rated}} \times \overline{(E/P)}_{\text{type}}$$

其中：
- $E_{\text{estimated}}$: 估算的能量容量 (kWh)
- $P_{\text{rated}}$: 项目的额定功率容量 (kW)  
- $\overline{(E/P)}_{\text{type}}$: 对应储能类型的加权平均E/P比值

### 4.3 估算验证
- 估算项目数: 772个
- 估算覆盖率: 约34%的项目能量容量通过此方法估算
- 方法优势: 保持了不同储能技术的典型特征

## 5. 数据筛选与质量控制

### 5.1 运行状态筛选
仅保留处于运行状态的储能项目：
- **筛选关键词**: `operational`, `operating`, `in operation`, `active`, `commissioned`
- **筛选结果**: 从2,250个项目筛选出1,610个运行状态项目

### 5.2 有效性验证
- **功率容量**: 排除功率容量≤0的项目
- **地理信息**: 保留有明确国家信息的项目
- **最终有效项目**: 1,602个项目成功映射到202个国家

## 6. 国家映射与标准化

### 6.1 映射挑战
原始数据中的国家名称存在多种表达方式，需要标准化到202个国家列表。

### 6.2 映射策略
采用三层映射方法：

#### 6.2.1 直接匹配
精确匹配国家名称

#### 6.2.2 别名映射
处理常见国家名称变体：
```python
country_aliases = {
    'USA': 'United States of America',
    'South Korea': 'Republic of Korea',
    'Czech Republic': 'Czech Republic',
    # ... 其他别名
}
```

#### 6.2.3 模糊匹配
处理拼写差异、连字符、下划线等变体

### 6.3 映射结果
- **成功映射**: 1,602个项目 (99.5%)
- **覆盖国家**: 73个国家拥有储能项目
- **未映射项目**: 8个项目来自小型海外领土

## 7. 最终数据统计

### 7.1 全球储能装机容量

| 储能类型 | 项目数 | 功率容量 (MW) | 能量容量 (MWh) | 占比 |
|---------|--------|---------------|----------------|------|
| 机械储能 | 463 | 170,216.1 | 6,599,647.1 | 96.6% |
| 电化学储能 | 1,139 | 5,995.4 | 12,954.0 | 3.4% |
| **总计** | **1,602** | **176,211.5** | **6,612,601.1** | **100%** |

### 7.2 主要储能国家 (前10名，按总功率容量排序)

| 排名 | 国家 | 总容量 (MW) | 机械储能 (MW) | 电化学储能 (MW) |
|------|------|-------------|---------------|----------------|
| 1 | 中国 | 31,459.9 | 31,399.6 | 60.3 |
| 2 | 日本 | 28,107.4 | 27,852.0 | 255.4 |
| 3 | 美国 | 23,892.4 | 18,997.8 | 4,894.6 |
| 4 | 西班牙 | 8,470.3 | 8,459.2 | 11.1 |
| 5 | 德国 | 7,948.0 | 7,777.8 | 170.1 |
| 6 | 意大利 | 7,129.5 | 7,070.7 | 58.8 |
| 7 | 印度 | 6,772.1 | 6,772.0 | 0.1 |
| 8 | 瑞士 | 6,374.6 | 6,372.5 | 2.1 |
| 9 | 奥地利 | 6,307.6 | 6,305.0 | 2.6 |
| 10 | 法国 | 5,920.0 | 5,908.4 | 11.6 |

## 8. 方法学创新点

### 8.1 简化分类体系
- 将复杂的储能技术分类简化为机械/电化学二分法
- 符合电力系统规划模型的应用需求
- 突出了不同储能技术的核心功能差异

### 8.2 加权平均E/P比值方法
- 基于功率容量加权，更准确反映技术特征
- 为缺失数据提供了科学的估算基础
- 保持了储能技术的典型运行特性

### 8.3 多层次国家映射
- 解决了数据库中国家名称不统一的问题
- 实现了99.5%的高映射成功率
- 支持标准化的国际比较分析

## 9. 数据限制与不确定性

### 9.1 数据完整性
- 部分项目缺失能量容量信息，通过估算方法补充
- 小型分布式储能项目可能未完全收录
- 数据更新频率限制可能影响最新项目的覆盖

### 9.2 分类精度
- 基于关键词的自动分类可能存在误分类
- 新兴储能技术的分类可能需要手动调整
- 技术边界模糊的项目分类存在主观性

### 9.3 地理覆盖
- 发展中国家的数据完整性可能低于发达国家
- 部分国家的储能项目可能未被充分记录
- 海外领土和特殊行政区的数据处理存在复杂性

## 10. 结论

本研究建立了一套完整的全球储能数据处理方法学，成功将GESDB原始数据转换为适用于电力系统扩展规划模型的标准化数据集。通过机械/电化学储能的二分类方法、基于加权平均的E/P比值估算、以及多层次的国家映射策略，为202个国家提供了高质量的储能装机容量数据。该方法学为大尺度电力系统规划研究提供了可靠的数据基础，支持全球能源转型路径的量化分析。

---

**数据可用性声明**: 处理后的储能数据以Excel和CSV格式提供，包含各国机械储能和电化学储能的详细容量信息，可用于进一步的研究和政策分析。

**方法学代码**: 完整的数据处理代码以Python脚本形式提供（`storage_analysis_202_countries.py`），确保研究结果的可重现性。 