import json
import pandas as pd
import numpy as np
from collections import defaultdict
import os

def load_storage_data(file_path):
    """加载全球储能数据库JSON文件"""
    print(f"正在加载数据文件: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"错误：文件不存在 {file_path}")
        return None
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"成功加载 {len(data)} 条记录")
        return data
    except Exception as e:
        print(f"加载文件时出错: {e}")
        return None

def safe_get_string(value, field_name="未知字段"):
    """安全获取字符串值，处理None值"""
    if value is None:
        return ""
    elif isinstance(value, str):
        return value.strip()
    else:
        return str(value).strip()

def safe_get_number(value, default=0):
    """安全获取数值，处理None值"""
    if value is None:
        return default
    try:
        return float(value) if value != '' else default
    except (ValueError, TypeError):
        return default

def extract_storage_info(data):
    """提取关键储能信息，并详细记录错误"""
    print("正在提取关键信息...")
    
    extracted_data = []
    error_details = []
    
    for record in data:
        try:
            record_id = record.get('ID', 'Unknown')
            
            # 安全提取基本信息
            try:
                latitude = record.get('Latitude')
                longitude = record.get('Longitude')
                country = safe_get_string(record.get('Country'), "Country")
                status = safe_get_string(record.get('Status'), "Status")
                data_source = safe_get_string(record.get('Data Source'), "Data Source")
                project_name = safe_get_string(record.get('Project/Plant Name'), "Project/Plant Name")
                
                # 提取功率容量 (kW)
                rated_power_kw = safe_get_number(record.get('Rated Power (kW)'), 0)
                
                # 提取能量容量 (kWh) - 从两个可能的位置获取
                storage_capacity_kwh = safe_get_number(record.get('Storage Capacity (kWh)'), 0)
                if storage_capacity_kwh == 0:
                    # 从Storage Device字段中获取
                    storage_device = record.get('Storage Device', {})
                    if isinstance(storage_device, dict):
                        storage_capacity_kwh = safe_get_number(storage_device.get('Storage Capacity (kWh)'), 0)
                
                # 提取储能技术类型
                storage_device = record.get('Storage Device', {})
                if isinstance(storage_device, dict):
                    tech_mid_type = safe_get_string(storage_device.get('Technology Mid-Type'), "Technology Mid-Type")
                    tech_broad_category = safe_get_string(storage_device.get('Technology Broad Category'), "Technology Broad Category")
                else:
                    tech_mid_type = ''
                    tech_broad_category = ''
                
                # 确定储能类型 (抽蓄 vs 电池储能)
                storage_type = determine_storage_type(tech_mid_type, tech_broad_category)
                
                # 提取更多详细信息
                city = safe_get_string(record.get('City'), "City")
                state_province = safe_get_string(record.get('State/Province/Territory'), "State/Province/Territory")
                commissioned_date = safe_get_string(record.get('Commissioned Date'), "Commissioned Date")
                utility_type = safe_get_string(record.get('Utility Type'), "Utility Type")
                owner = safe_get_string(record.get('Owner(s)'), "Owner(s)")
                
                # 提取Power Conversion System信息
                pcs = record.get('Power Conversion System', {})
                if isinstance(pcs, dict):
                    pcs_rated_power = safe_get_number(pcs.get('Rated Power (kW)'), 0)
                    max_charge_power = safe_get_number(pcs.get('Maximum Charge Power (kW)'), 0)
                    max_discharge_power = safe_get_number(pcs.get('Maximum Discharge Power (kW)'), 0)
                else:
                    pcs_rated_power = 0
                    max_charge_power = 0
                    max_discharge_power = 0
                
                # 记录所有信息（包括功率为0的记录，用于完整性分析）
                extracted_data.append({
                    'ID': record_id,
                    'Project_Name': project_name,
                    'Latitude': latitude,
                    'Longitude': longitude,
                    'Country': country,
                    'City': city,
                    'State_Province': state_province,
                    'Status': status,
                    'Rated_Power_kW': rated_power_kw,
                    'Storage_Capacity_kWh': storage_capacity_kwh,
                    'Storage_Type': storage_type,
                    'Tech_Mid_Type': tech_mid_type,
                    'Tech_Broad_Category': tech_broad_category,
                    'Commissioned_Date': commissioned_date,
                    'Utility_Type': utility_type,
                    'Owner': owner,
                    'PCS_Rated_Power_kW': pcs_rated_power,
                    'Max_Charge_Power_kW': max_charge_power,
                    'Max_Discharge_Power_kW': max_discharge_power,
                    'Data_Source': data_source,
                    'Has_Valid_Power': rated_power_kw > 0,
                    'Has_Valid_Country': country != "",
                    'Has_Valid_Location': latitude is not None and longitude is not None
                })
                
            except Exception as field_error:
                error_details.append({
                    'Record_ID': record_id,
                    'Error_Type': '字段提取错误',
                    'Error_Message': str(field_error),
                    'Problematic_Fields': get_none_fields(record)
                })
                continue
                
        except Exception as e:
            error_details.append({
                'Record_ID': record.get('ID', 'Unknown'),
                'Error_Type': '记录处理错误',
                'Error_Message': str(e),
                'Problematic_Fields': get_none_fields(record)
            })
            continue
    
    print(f"成功提取 {len(extracted_data)} 条记录")
    if error_details:
        print(f"遇到 {len(error_details)} 个错误记录")
        
        # 保存错误详情
        save_error_details(error_details)
    
    return extracted_data

def get_none_fields(record):
    """获取记录中为None的字段列表"""
    none_fields = []
    key_fields = ['Country', 'Status', 'Data Source', 'Project/Plant Name', 
                  'Rated Power (kW)', 'Storage Capacity (kWh)']
    
    for field in key_fields:
        if record.get(field) is None:
            none_fields.append(field)
    
    # 检查Storage Device中的字段
    storage_device = record.get('Storage Device', {})
    if isinstance(storage_device, dict):
        storage_fields = ['Technology Mid-Type', 'Technology Broad Category']
        for field in storage_fields:
            if storage_device.get(field) is None:
                none_fields.append(f"Storage Device.{field}")
    
    return none_fields

def save_error_details(error_details):
    """保存错误详情到文件"""
    error_df = pd.DataFrame(error_details)
    error_file = "数据处理错误详情.xlsx"
    error_df.to_excel(error_file, index=False, sheet_name="错误详情")
    print(f"错误详情已保存到: {error_file}")

def determine_storage_type(tech_mid_type, tech_broad_category):
    """根据技术类型确定储能类型"""
    tech_mid_type = tech_mid_type.lower()
    tech_broad_category = tech_broad_category.lower()
    
    # 抽蓄关键词
    pumped_hydro_keywords = ['pumped hydro', 'pumped storage', 'pumped-storage', 'phs']
    
    # 电池储能关键词
    battery_keywords = ['battery', 'lithium', 'lead acid', 'sodium', 'flow battery', 'vanadium']
    
    # 检查是否为抽蓄
    if any(keyword in tech_mid_type for keyword in pumped_hydro_keywords):
        return '抽蓄'
    
    # 检查是否为电池储能
    if any(keyword in tech_mid_type for keyword in battery_keywords) or \
       any(keyword in tech_broad_category for keyword in battery_keywords):
        return '电池储能'
    
    # 其他电化学储能也归类为电池储能
    if 'electrochemical' in tech_broad_category:
        return '电池储能'
    
    # 机械储能（除抽蓄外）
    if 'mechanical' in tech_broad_category and not any(keyword in tech_mid_type for keyword in pumped_hydro_keywords):
        return '其他机械储能'
    
    # 其他类型
    return '其他储能'

def save_all_projects(data, filename="全球储能项目详细清单.xlsx"):
    """保存所有项目的详细信息到Excel"""
    print(f"正在保存所有项目详细信息到 {filename}...")
    
    df = pd.DataFrame(data)
    
    # 创建多个工作表
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        # 主工作表：所有项目
        df.to_excel(writer, sheet_name="所有项目", index=False)
        
        # 按状态分类
        operational_df = df[df['Status'].str.contains('operational|operating|commissioned', case=False, na=False)]
        operational_df.to_excel(writer, sheet_name="运行状态项目", index=False)
        
        # 按储能类型分类
        pumped_hydro_df = df[df['Storage_Type'] == '抽蓄']
        battery_df = df[df['Storage_Type'] == '电池储能']
        other_df = df[df['Storage_Type'].isin(['其他机械储能', '其他储能'])]
        
        if not pumped_hydro_df.empty:
            pumped_hydro_df.to_excel(writer, sheet_name="抽蓄项目", index=False)
        if not battery_df.empty:
            battery_df.to_excel(writer, sheet_name="电池储能项目", index=False)
        if not other_df.empty:
            other_df.to_excel(writer, sheet_name="其他储能项目", index=False)
        
        # 数据质量统计
        quality_stats = pd.DataFrame({
            '统计项目': [
                '总项目数',
                '有效功率数据项目数',
                '有国家信息项目数',
                '有位置信息项目数',
                '运行状态项目数',
                '抽蓄项目数',
                '电池储能项目数',
                '其他储能项目数'
            ],
            '数量': [
                len(df),
                len(df[df['Has_Valid_Power'] == True]),
                len(df[df['Has_Valid_Country'] == True]),
                len(df[df['Has_Valid_Location'] == True]),
                len(operational_df),
                len(pumped_hydro_df),
                len(battery_df),
                len(other_df)
            ]
        })
        quality_stats.to_excel(writer, sheet_name="数据质量统计", index=False)
    
    print(f"项目详细信息已保存到: {filename}")

def filter_operational_storage(data):
    """筛选处于运行状态的储能项目"""
    print("正在筛选运行状态的储能项目...")
    
    operational_keywords = ['operational', 'operating', 'in operation', 'active', 'commissioned']
    
    operational_data = []
    for record in data:
        status = record['Status'].lower()
        # 只保留有效功率和国家信息的运行状态项目
        if (any(keyword in status for keyword in operational_keywords) and 
            record['Has_Valid_Power'] and record['Has_Valid_Country']):
            operational_data.append(record)
    
    print(f"筛选出 {len(operational_data)} 个有效的运行状态储能项目")
    return operational_data

def aggregate_by_country(data):
    """按国家聚合储能容量"""
    print("正在按国家聚合数据...")
    
    country_aggregation = defaultdict(lambda: {
        '抽蓄_项目数': 0,
        '抽蓄_功率容量_MW': 0,
        '抽蓄_能量容量_MWh': 0,
        '电池储能_项目数': 0,
        '电池储能_功率容量_MW': 0,
        '电池储能_能量容量_MWh': 0,
        '其他储能_项目数': 0,
        '其他储能_功率容量_MW': 0,
        '其他储能_能量容量_MWh': 0,
        '总项目数': 0,
        '总功率容量_MW': 0,
        '总能量容量_MWh': 0
    })
    
    for record in data:
        country = record['Country']
        storage_type = record['Storage_Type']
        power_mw = record['Rated_Power_kW'] / 1000  # 转换为MW
        energy_mwh = record['Storage_Capacity_kWh'] / 1000  # 转换为MWh
        
        # 按储能类型统计
        if storage_type == '抽蓄':
            country_aggregation[country]['抽蓄_项目数'] += 1
            country_aggregation[country]['抽蓄_功率容量_MW'] += power_mw
            country_aggregation[country]['抽蓄_能量容量_MWh'] += energy_mwh
        elif storage_type == '电池储能':
            country_aggregation[country]['电池储能_项目数'] += 1
            country_aggregation[country]['电池储能_功率容量_MW'] += power_mw
            country_aggregation[country]['电池储能_能量容量_MWh'] += energy_mwh
        else:
            country_aggregation[country]['其他储能_项目数'] += 1
            country_aggregation[country]['其他储能_功率容量_MW'] += power_mw
            country_aggregation[country]['其他储能_能量容量_MWh'] += energy_mwh
        
        # 总计统计
        country_aggregation[country]['总项目数'] += 1
        country_aggregation[country]['总功率容量_MW'] += power_mw
        country_aggregation[country]['总能量容量_MWh'] += energy_mwh
    
    print(f"聚合了 {len(country_aggregation)} 个国家的数据")
    return dict(country_aggregation)

def create_summary_report(country_data):
    """创建汇总报告"""
    print("正在生成汇总报告...")
    
    # 转换为DataFrame便于分析和导出
    df = pd.DataFrame.from_dict(country_data, orient='index')
    df.index.name = '国家'
    df = df.sort_values('总功率容量_MW', ascending=False)
    
    # 计算全球总计
    global_totals = {
        '全球抽蓄项目数': df['抽蓄_项目数'].sum(),
        '全球抽蓄功率容量_MW': df['抽蓄_功率容量_MW'].sum(),
        '全球抽蓄能量容量_MWh': df['抽蓄_能量容量_MWh'].sum(),
        '全球电池储能项目数': df['电池储能_项目数'].sum(),
        '全球电池储能功率容量_MW': df['电池储能_功率容量_MW'].sum(),
        '全球电池储能能量容量_MWh': df['电池储能_能量容量_MWh'].sum(),
        '全球其他储能项目数': df['其他储能_项目数'].sum(),
        '全球其他储能功率容量_MW': df['其他储能_功率容量_MW'].sum(),
        '全球其他储能能量容量_MWh': df['其他储能_能量容量_MWh'].sum(),
        '全球总项目数': df['总项目数'].sum(),
        '全球总功率容量_MW': df['总功率容量_MW'].sum(),
        '全球总能量容量_MWh': df['总能量容量_MWh'].sum()
    }
    
    return df, global_totals

def print_top_countries(df, top_n=10):
    """打印前N个国家的储能情况"""
    print(f"\n=== 全球前{top_n}个储能大国 (按总功率容量排序) ===")
    print("-" * 120)
    
    top_countries = df.head(top_n)
    
    for i, (country, row) in enumerate(top_countries.iterrows(), 1):
        print(f"\n{i}. {country}")
        print(f"   总功率容量: {row['总功率容量_MW']:,.1f} MW")
        print(f"   抽蓄: {row['抽蓄_功率容量_MW']:,.1f} MW ({row['抽蓄_项目数']:.0f} 个项目)")
        print(f"   电池储能: {row['电池储能_功率容量_MW']:,.1f} MW ({row['电池储能_项目数']:.0f} 个项目)")
        if row['其他储能_功率容量_MW'] > 0:
            print(f"   其他储能: {row['其他储能_功率容量_MW']:,.1f} MW ({row['其他储能_项目数']:.0f} 个项目)")

def save_results(df, global_totals):
    """保存分析结果"""
    print(f"\n正在保存结果...")
    
    # 保存详细的国家级数据
    country_file = "各国储能容量统计.xlsx"
    df.to_excel(country_file, sheet_name="各国储能统计", float_format="%.2f")
    
    # 保存全球汇总数据
    global_df = pd.DataFrame.from_dict(global_totals, orient='index', columns=['数值'])
    global_df.index.name = '指标'
    global_file = "全球储能容量汇总.xlsx"
    global_df.to_excel(global_file, sheet_name="全球汇总")
    
    # 保存CSV格式
    csv_file = "各国储能容量统计.csv"
    df.to_csv(csv_file, encoding='utf-8-sig', float_format="%.2f")
    
    print(f"结果已保存到:")
    print(f"  - {country_file}")
    print(f"  - {global_file}")
    print(f"  - {csv_file}")

def main():
    """主函数"""
    print("=" * 80)
    print("         全球储能数据库分析系统 (改进版)")
    print("=" * 80)
    
    # 加载数据
    json_file = "GESDB_Project_Data.json"
    raw_data = load_storage_data(json_file)
    
    if raw_data is None:
        return
    
    # 提取关键信息
    extracted_data = extract_storage_info(raw_data)
    
    if not extracted_data:
        print("没有找到有效的储能数据")
        return
    
    # 保存所有项目详细信息
    save_all_projects(extracted_data)
    
    # 筛选运行状态的储能
    operational_data = filter_operational_storage(extracted_data)
    
    if not operational_data:
        print("没有找到运行状态的储能项目")
        return
    
    # 按国家聚合
    country_data = aggregate_by_country(operational_data)
    
    # 生成汇总报告
    df, global_totals = create_summary_report(country_data)
    
    # 打印全球汇总信息
    print("\n" + "=" * 80)
    print("                    全球储能容量汇总")
    print("=" * 80)
    
    print(f"\n运行状态储能项目总数: {global_totals['全球总项目数']:,} 个")
    print(f"总功率容量: {global_totals['全球总功率容量_MW']:,.1f} MW")
    print(f"总能量容量: {global_totals['全球总能量容量_MWh']:,.1f} MWh")
    
    print(f"\n抽蓄储能:")
    print(f"  项目数: {global_totals['全球抽蓄项目数']:,} 个")
    print(f"  功率容量: {global_totals['全球抽蓄功率容量_MW']:,.1f} MW")
    print(f"  能量容量: {global_totals['全球抽蓄能量容量_MWh']:,.1f} MWh")
    
    print(f"\n电池储能:")
    print(f"  项目数: {global_totals['全球电池储能项目数']:,} 个")
    print(f"  功率容量: {global_totals['全球电池储能功率容量_MW']:,.1f} MW")
    print(f"  能量容量: {global_totals['全球电池储能能量容量_MWh']:,.1f} MWh")
    
    if global_totals['全球其他储能项目数'] > 0:
        print(f"\n其他储能:")
        print(f"  项目数: {global_totals['全球其他储能项目数']:,} 个")
        print(f"  功率容量: {global_totals['全球其他储能功率容量_MW']:,.1f} MW")
        print(f"  能量容量: {global_totals['全球其他储能能量容量_MWh']:,.1f} MWh")
    
    # 打印前15个储能大国
    print_top_countries(df, top_n=15)
    
    print(f"\n涉及的国家总数: {len(df)} 个")
    
    # 保存结果
    save_results(df, global_totals)
    
    print("\n" + "=" * 80)
    print("分析完成！")
    print("=" * 80)

if __name__ == "__main__":
    main() 