import json
import pandas as pd
import numpy as np
from collections import defaultdict
import os

def load_storage_data(file_path):
    """加载全球储能数据库JSON文件"""
    print(f"正在加载数据文件: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"错误：文件不存在 {file_path}")
        return None
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"成功加载 {len(data)} 条记录")
        return data
    except Exception as e:
        print(f"加载文件时出错: {e}")
        return None

def load_country_mapping():
    """加载202个国家的映射表"""
    print("正在加载国家映射表...")
    
    try:
        df = pd.read_excel('Capacity_2023_Clean.xlsx')
        country_mapping = {}
        
        for _, row in df.iterrows():
            iso_code = row['ISO_Code']
            country = row['Country']
            country_mapping[country] = iso_code
            
        print(f"成功加载 {len(country_mapping)} 个国家的映射关系")
        return country_mapping, df
    except Exception as e:
        print(f"加载国家映射表时出错: {e}")
        return {}, pd.DataFrame()

def safe_get_string(value, field_name="未知字段"):
    """安全获取字符串值，处理None值"""
    if value is None:
        return ""
    elif isinstance(value, str):
        return value.strip()
    else:
        return str(value).strip()

def safe_get_number(value, default=0):
    """安全获取数值，处理None值"""
    if value is None:
        return default
    try:
        return float(value) if value != '' else default
    except (ValueError, TypeError):
        return default

def classify_storage_new(tech_mid_type, tech_broad_category):
    """新的储能分类：只分为机械储能和电化学储能"""
    tech_mid_type = tech_mid_type.lower()
    tech_broad_category = tech_broad_category.lower()
    
    # 机械储能：主要是抽蓄
    mechanical_keywords = ['pumped hydro', 'pumped storage', 'pumped-storage', 'phs']
    
    # 电化学储能：各种电池
    electrochemical_keywords = ['battery', 'lithium', 'lead acid', 'sodium', 'flow battery', 
                               'vanadium', 'electrochemical', 'nickel', 'zinc']
    
    # 热储能关键词（需要排除）
    thermal_keywords = ['thermal', 'molten salt', 'phase change', 'sensible heat']
    
    # 先排除热储能
    if any(keyword in tech_mid_type for keyword in thermal_keywords) or \
       any(keyword in tech_broad_category for keyword in thermal_keywords):
        return '热储能'  # 这类项目将被忽略
    
    # 判断机械储能
    if any(keyword in tech_mid_type for keyword in mechanical_keywords):
        return '机械储能'
    
    # 判断电化学储能
    if any(keyword in tech_mid_type for keyword in electrochemical_keywords) or \
       any(keyword in tech_broad_category for keyword in electrochemical_keywords):
        return '电化学储能'
    
    # 其他机械储能（如压缩空气、飞轮等）
    if 'mechanical' in tech_broad_category:
        return '机械储能'
    
    # 默认归类为电化学储能（大部分未分类的现代储能都是电池类）
    return '电化学储能'

def extract_storage_info_new(data):
    """按新分类方法提取储能信息"""
    print("正在按新分类方法提取储能信息...")
    
    extracted_data = []
    
    for record in data:
        try:
            record_id = record.get('ID', 'Unknown')
            
            # 基本信息提取
            latitude = record.get('Latitude')
            longitude = record.get('Longitude')
            country = safe_get_string(record.get('Country'), "Country")
            status = safe_get_string(record.get('Status'), "Status")
            project_name = safe_get_string(record.get('Project/Plant Name'), "Project/Plant Name")
            
            # 功率容量 (kW)
            rated_power_kw = safe_get_number(record.get('Rated Power (kW)'), 0)
            
            # 能量容量 (kWh)
            storage_capacity_kwh = safe_get_number(record.get('Storage Capacity (kWh)'), 0)
            if storage_capacity_kwh == 0:
                storage_device = record.get('Storage Device', {})
                if isinstance(storage_device, dict):
                    storage_capacity_kwh = safe_get_number(storage_device.get('Storage Capacity (kWh)'), 0)
            
            # 技术类型
            storage_device = record.get('Storage Device', {})
            if isinstance(storage_device, dict):
                tech_mid_type = safe_get_string(storage_device.get('Technology Mid-Type'), "Technology Mid-Type")
                tech_broad_category = safe_get_string(storage_device.get('Technology Broad Category'), "Technology Broad Category")
            else:
                tech_mid_type = ''
                tech_broad_category = ''
            
            # 新的分类方法
            storage_type_new = classify_storage_new(tech_mid_type, tech_broad_category)
            
            # 只保留机械储能和电化学储能，忽略热储能
            if storage_type_new in ['机械储能', '电化学储能'] and country and rated_power_kw > 0:
                
                # 计算能量功率比
                energy_power_ratio = 0
                if storage_capacity_kwh > 0 and rated_power_kw > 0:
                    energy_power_ratio = storage_capacity_kwh / rated_power_kw
                
                extracted_data.append({
                    'ID': record_id,
                    'Project_Name': project_name,
                    'Country': country,
                    'Status': status,
                    'Rated_Power_kW': rated_power_kw,
                    'Storage_Capacity_kWh': storage_capacity_kwh,
                    'Storage_Type_New': storage_type_new,
                    'Tech_Mid_Type': tech_mid_type,
                    'Tech_Broad_Category': tech_broad_category,
                    'Energy_Power_Ratio': energy_power_ratio,
                    'Has_Energy_Capacity': storage_capacity_kwh > 0,
                    'Latitude': latitude,
                    'Longitude': longitude
                })
                
        except Exception as e:
            print(f"处理记录 {record.get('ID', 'Unknown')} 时出错: {e}")
            continue
    
    print(f"成功提取 {len(extracted_data)} 条有效记录（机械储能和电化学储能）")
    return extracted_data

def calculate_weighted_average_ratios(data):
    """计算加权平均能量功率比"""
    print("正在计算加权平均能量功率比...")
    
    # 只考虑有能量容量数据的项目
    valid_data = [record for record in data if record['Has_Energy_Capacity']]
    
    # 按储能类型分组计算
    mechanical_data = [r for r in valid_data if r['Storage_Type_New'] == '机械储能']
    electrochemical_data = [r for r in valid_data if r['Storage_Type_New'] == '电化学储能']
    
    def calculate_weighted_ratio(group_data):
        if not group_data:
            return 0
        
        total_power = sum(r['Rated_Power_kW'] for r in group_data)
        if total_power == 0:
            return 0
        
        weighted_sum = sum(r['Energy_Power_Ratio'] * r['Rated_Power_kW'] for r in group_data)
        return weighted_sum / total_power
    
    mechanical_avg_ratio = calculate_weighted_ratio(mechanical_data)
    electrochemical_avg_ratio = calculate_weighted_ratio(electrochemical_data)
    
    print(f"机械储能加权平均能量功率比: {mechanical_avg_ratio:.2f} h")
    print(f"电化学储能加权平均能量功率比: {electrochemical_avg_ratio:.2f} h")
    print(f"用于计算的机械储能项目数: {len(mechanical_data)}")
    print(f"用于计算的电化学储能项目数: {len(electrochemical_data)}")
    
    return mechanical_avg_ratio, electrochemical_avg_ratio

def estimate_missing_energy_capacity(data, mechanical_avg_ratio, electrochemical_avg_ratio):
    """为缺失能量容量的项目估算能量容量"""
    print("正在为缺失能量容量的项目估算数据...")
    
    updated_data = []
    estimated_count = 0
    
    for record in data:
        new_record = record.copy()
        
        if not record['Has_Energy_Capacity']:
            # 根据储能类型使用相应的平均比例估算
            if record['Storage_Type_New'] == '机械储能':
                estimated_capacity = record['Rated_Power_kW'] * mechanical_avg_ratio
                new_record['Storage_Capacity_kWh_Estimated'] = estimated_capacity
                estimated_count += 1
            elif record['Storage_Type_New'] == '电化学储能':
                estimated_capacity = record['Rated_Power_kW'] * electrochemical_avg_ratio
                new_record['Storage_Capacity_kWh_Estimated'] = estimated_capacity
                estimated_count += 1
            else:
                new_record['Storage_Capacity_kWh_Estimated'] = 0
        else:
            # 使用原有的能量容量
            new_record['Storage_Capacity_kWh_Estimated'] = record['Storage_Capacity_kWh']
        
        updated_data.append(new_record)
    
    print(f"为 {estimated_count} 个项目估算了能量容量")
    return updated_data

def filter_operational_storage(data):
    """筛选运行状态的储能项目"""
    print("正在筛选运行状态的储能项目...")
    
    operational_keywords = ['operational', 'operating', 'in operation', 'active', 'commissioned']
    
    operational_data = []
    for record in data:
        status = record['Status'].lower()
        if any(keyword in status for keyword in operational_keywords):
            operational_data.append(record)
    
    print(f"筛选出 {len(operational_data)} 个运行状态的储能项目")
    return operational_data

def map_countries_to_202_list(data, country_mapping):
    """将项目映射到202个国家列表"""
    print("正在将项目映射到202个国家列表...")
    
    # 创建国家名称别名映射字典
    country_aliases = {
        'USA': 'United States of America',
        'US': 'United States of America',
        'United States': 'United States of America',
        'South Korea': 'Republic of Korea',
        'Korea': 'Republic of Korea',
        'North Korea': 'North Korea',
        'Russian Federation': 'Russia',
        'Czech Republic': 'Czech Republic',
        'Burkina-Faso': 'Burkina Faso',
        'Cape Verde': 'Cabo Verde'
    }
    
    mapped_data = []
    unmapped_countries = set()
    
    for record in data:
        original_country = record['Country']
        matched = False
        
        # 1. 直接匹配
        if original_country in country_mapping:
            record['ISO_Code'] = country_mapping[original_country]
            record['Standard_Country'] = original_country
            mapped_data.append(record)
            matched = True
        else:
            # 2. 检查别名映射
            for alias, standard_name in country_aliases.items():
                if original_country.lower() == alias.lower():
                    if standard_name in country_mapping:
                        record['ISO_Code'] = country_mapping[standard_name]
                        record['Standard_Country'] = standard_name
                        mapped_data.append(record)
                        matched = True
                        break
            
            # 3. 模糊匹配
            if not matched:
                for std_country, iso_code in country_mapping.items():
                    # 检查包含关系和去空格匹配
                    if (original_country.lower() in std_country.lower() or 
                        std_country.lower() in original_country.lower() or
                        original_country.lower().replace(' ', '') == std_country.lower().replace(' ', '') or
                        original_country.lower().replace('_', ' ') == std_country.lower() or
                        original_country.lower().replace('-', ' ') == std_country.lower()):
                        record['ISO_Code'] = iso_code
                        record['Standard_Country'] = std_country
                        mapped_data.append(record)
                        matched = True
                        break
        
        if not matched:
            unmapped_countries.add(original_country)
    
    if unmapped_countries:
        print(f"以下 {len(unmapped_countries)} 个国家无法映射到202国家列表：")
        for country in sorted(unmapped_countries):
            print(f"  - {country}")
    
    print(f"成功映射 {len(mapped_data)} 个项目到202个国家")
    return mapped_data

def aggregate_by_202_countries(data, country_df):
    """按202个国家聚合储能数据"""
    print("正在按202个国家聚合储能数据...")
    
    # 初始化所有202个国家的数据
    country_data = {}
    for _, row in country_df.iterrows():
        iso_code = row['ISO_Code']
        country = row['Country']
        country_data[iso_code] = {
            'Country': country,
            'ISO_Code': iso_code,
            '机械储能_项目数': 0,
            '机械储能_功率容量_MW': 0.0,
            '机械储能_能量容量_MWh': 0.0,
            '电化学储能_项目数': 0,
            '电化学储能_功率容量_MW': 0.0,
            '电化学储能_能量容量_MWh': 0.0,
            '总储能_项目数': 0,
            '总储能_功率容量_MW': 0.0,
            '总储能_能量容量_MWh': 0.0
        }
    
    # 聚合数据
    for record in data:
        iso_code = record['ISO_Code']
        storage_type = record['Storage_Type_New']
        power_mw = record['Rated_Power_kW'] / 1000  # 转换为MW
        energy_mwh = record['Storage_Capacity_kWh_Estimated'] / 1000  # 转换为MWh
        
        if iso_code in country_data:
            # 按储能类型统计
            if storage_type == '机械储能':
                country_data[iso_code]['机械储能_项目数'] += 1
                country_data[iso_code]['机械储能_功率容量_MW'] += power_mw
                country_data[iso_code]['机械储能_能量容量_MWh'] += energy_mwh
            elif storage_type == '电化学储能':
                country_data[iso_code]['电化学储能_项目数'] += 1
                country_data[iso_code]['电化学储能_功率容量_MW'] += power_mw
                country_data[iso_code]['电化学储能_能量容量_MWh'] += energy_mwh
            
            # 总计
            country_data[iso_code]['总储能_项目数'] += 1
            country_data[iso_code]['总储能_功率容量_MW'] += power_mw
            country_data[iso_code]['总储能_能量容量_MWh'] += energy_mwh
    
    # 筛选出有储能项目的国家
    countries_with_storage = {k: v for k, v in country_data.items() if v['总储能_项目数'] > 0}
    
    print(f"202个国家中有 {len(countries_with_storage)} 个国家拥有储能项目")
    return country_data

def save_results(all_data, projects_data, mechanical_avg_ratio, electrochemical_avg_ratio):
    """保存分析结果"""
    print("正在保存分析结果...")
    
    # 1. 保存项目详细信息
    projects_df = pd.DataFrame(projects_data)
    projects_file = "储能项目详细信息_202国分类.xlsx"
    
    with pd.ExcelWriter(projects_file, engine='openpyxl') as writer:
        projects_df.to_excel(writer, sheet_name="所有运行状态项目", index=False)
        
        # 按储能类型分类
        mechanical_df = projects_df[projects_df['Storage_Type_New'] == '机械储能']
        electrochemical_df = projects_df[projects_df['Storage_Type_New'] == '电化学储能']
        
        if not mechanical_df.empty:
            mechanical_df.to_excel(writer, sheet_name="机械储能项目", index=False)
        if not electrochemical_df.empty:
            electrochemical_df.to_excel(writer, sheet_name="电化学储能项目", index=False)
        
        # 统计信息
        stats_data = [
            ['总项目数', len(projects_df)],
            ['机械储能项目数', len(mechanical_df)],
            ['电化学储能项目数', len(electrochemical_df)],
            ['机械储能加权平均能量功率比(h)', f'{mechanical_avg_ratio:.3f}'],
            ['电化学储能加权平均能量功率比(h)', f'{electrochemical_avg_ratio:.3f}'],
            ['机械储能总功率(MW)', f'{mechanical_df["Rated_Power_kW"].sum()/1000:.2f}'],
            ['电化学储能总功率(MW)', f'{electrochemical_df["Rated_Power_kW"].sum()/1000:.2f}'],
            ['机械储能总能量(MWh)', f'{mechanical_df["Storage_Capacity_kWh_Estimated"].sum()/1000:.2f}'],
            ['电化学储能总能量(MWh)', f'{electrochemical_df["Storage_Capacity_kWh_Estimated"].sum()/1000:.2f}']
        ]
        stats_df = pd.DataFrame(stats_data, columns=['指标', '数值'])
        stats_df.to_excel(writer, sheet_name="统计信息", index=False)
    
    # 2. 保存202个国家的聚合数据
    countries_df = pd.DataFrame.from_dict(all_data, orient='index')
    countries_df = countries_df.sort_values('总储能_功率容量_MW', ascending=False)
    
    # 分别保存完整的202国数据和有储能的国家数据
    countries_file = "202个国家储能容量统计.xlsx"
    with pd.ExcelWriter(countries_file, engine='openpyxl') as writer:
        # 所有202个国家
        countries_df.to_excel(writer, sheet_name="202个国家完整列表", index=False)
        
        # 只有储能项目的国家
        countries_with_storage = countries_df[countries_df['总储能_项目数'] > 0]
        countries_with_storage.to_excel(writer, sheet_name="有储能项目的国家", index=False)
        
        # 按储能类型排序的国家列表
        mechanical_top = countries_df.sort_values('机械储能_功率容量_MW', ascending=False).head(20)
        electrochemical_top = countries_df.sort_values('电化学储能_功率容量_MW', ascending=False).head(20)
        
        mechanical_top.to_excel(writer, sheet_name="机械储能前20国", index=False)
        electrochemical_top.to_excel(writer, sheet_name="电化学储能前20国", index=False)
    
    # 3. 保存CSV格式
    csv_file = "202个国家储能容量统计.csv"
    countries_df.to_csv(csv_file, encoding='utf-8-sig', index=False, float_format="%.3f")
    
    print(f"结果已保存到:")
    print(f"  - {projects_file}")
    print(f"  - {countries_file}")
    print(f"  - {csv_file}")

def print_summary(all_data, projects_data, mechanical_avg_ratio, electrochemical_avg_ratio):
    """打印分析摘要"""
    countries_df = pd.DataFrame.from_dict(all_data, orient='index')
    countries_with_storage = countries_df[countries_df['总储能_项目数'] > 0]
    
    mechanical_projects = [p for p in projects_data if p['Storage_Type_New'] == '机械储能']
    electrochemical_projects = [p for p in projects_data if p['Storage_Type_New'] == '电化学储能']
    
    print("\n" + "=" * 80)
    print("                202个国家储能分析摘要")
    print("=" * 80)
    
    print(f"\n【分类方法】")
    print(f"机械储能：抽蓄等机械储能技术")
    print(f"电化学储能：锂电池等各类电池储能技术")
    print(f"已忽略：热储能等其他储能技术")
    
    print(f"\n【能量功率比】")
    print(f"机械储能加权平均能量功率比: {mechanical_avg_ratio:.3f} 小时")
    print(f"电化学储能加权平均能量功率比: {electrochemical_avg_ratio:.3f} 小时")
    
    print(f"\n【项目统计】")
    print(f"运行状态项目总数: {len(projects_data):,} 个")
    print(f"  机械储能: {len(mechanical_projects):,} 个")
    print(f"  电化学储能: {len(electrochemical_projects):,} 个")
    
    print(f"\n【容量统计】")
    total_mechanical_power = sum(p['Rated_Power_kW'] for p in mechanical_projects) / 1000
    total_electrochemical_power = sum(p['Rated_Power_kW'] for p in electrochemical_projects) / 1000
    total_mechanical_energy = sum(p['Storage_Capacity_kWh_Estimated'] for p in mechanical_projects) / 1000
    total_electrochemical_energy = sum(p['Storage_Capacity_kWh_Estimated'] for p in electrochemical_projects) / 1000
    
    print(f"机械储能总功率: {total_mechanical_power:,.1f} MW")
    print(f"机械储能总能量: {total_mechanical_energy:,.1f} MWh")
    print(f"电化学储能总功率: {total_electrochemical_power:,.1f} MW")
    print(f"电化学储能总能量: {total_electrochemical_energy:,.1f} MWh")
    
    print(f"\n【国家分布】")
    print(f"202个国家中有储能项目的国家: {len(countries_with_storage)} 个")
    
    print(f"\n【前10个储能大国（按总功率）】")
    top_countries = countries_with_storage.sort_values('总储能_功率容量_MW', ascending=False).head(10)
    for i, (_, row) in enumerate(top_countries.iterrows(), 1):
        print(f"{i:2d}. {row['Country']:20s} | 总计: {row['总储能_功率容量_MW']:8.1f} MW | "
              f"机械: {row['机械储能_功率容量_MW']:8.1f} MW | 电化学: {row['电化学储能_功率容量_MW']:8.1f} MW")

def main():
    """主函数"""
    print("=" * 80)
    print("    全球储能数据分析 - 202个国家机械/电化学储能分类")
    print("=" * 80)
    
    # 1. 加载数据
    json_file = "GESDB_Project_Data.json"
    raw_data = load_storage_data(json_file)
    if raw_data is None:
        return
    
    # 2. 加载202个国家映射
    country_mapping, country_df = load_country_mapping()
    if not country_mapping:
        return
    
    # 3. 提取储能信息（新分类）
    extracted_data = extract_storage_info_new(raw_data)
    if not extracted_data:
        print("没有找到有效的储能数据")
        return
    
    # 4. 计算加权平均能量功率比
    mechanical_avg_ratio, electrochemical_avg_ratio = calculate_weighted_average_ratios(extracted_data)
    
    # 5. 估算缺失的能量容量
    updated_data = estimate_missing_energy_capacity(extracted_data, mechanical_avg_ratio, electrochemical_avg_ratio)
    
    # 6. 筛选运行状态项目
    operational_data = filter_operational_storage(updated_data)
    if not operational_data:
        print("没有找到运行状态的储能项目")
        return
    
    # 7. 映射到202个国家
    mapped_data = map_countries_to_202_list(operational_data, country_mapping)
    if not mapped_data:
        print("没有成功映射的国家数据")
        return
    
    # 8. 按202个国家聚合
    aggregated_data = aggregate_by_202_countries(mapped_data, country_df)
    
    # 9. 打印摘要
    print_summary(aggregated_data, mapped_data, mechanical_avg_ratio, electrochemical_avg_ratio)
    
    # 10. 保存结果
    save_results(aggregated_data, mapped_data, mechanical_avg_ratio, electrochemical_avg_ratio)
    
    print("\n" + "=" * 80)
    print("分析完成！")
    print("=" * 80)

if __name__ == "__main__":
    main() 