#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全球电力负荷时序数据改进脚本（基于相似性匹配 + 时区调整）
作者: AI Assistant
日期: 2025-07-28

功能：
1. 基于GDP、人口等指标计算国家相似性
2. 为每个缺失详细时序数据的国家选择最相似的参考国家
3. 根据时区差异调整参考国家的负荷模式
4. 生成基于相似性加权和时区调整的个性化负荷曲线
5. 确保生成的时序数据总和严格等于原有的年度负荷总量
6. 添加适当的随机变化使负荷曲线更加真实
"""

import pandas as pd
import numpy as np
import warnings
from datetime import datetime
import os
from sklearn.preprocessing import StandardScaler
from sklearn.metrics.pairwise import euclidean_distances

# 忽略警告信息
warnings.filterwarnings('ignore')

class LoadDataImprover:
    """基于相似性匹配和时区调整的负荷数据改进器"""

    def __init__(self, input_file='Load_TimeSeries_2023.xlsx', output_file='Load_TimeSeries_2023_Improved_Timezone.xlsx',
                 gdp_file='GDP_INT-Export-07-28-2025_17-43-58.csv',
                 pop_file='Population_INT-Export-07-28-2025_17-44-34.csv'):
        """
        初始化改进器

        参数:
        input_file: 输入Excel文件路径
        output_file: 输出Excel文件路径
        gdp_file: GDP数据文件路径
        pop_file: 人口数据文件路径
        """
        self.input_file = input_file
        self.output_file = output_file
        self.gdp_file = gdp_file
        self.pop_file = pop_file

        # 需要改进的16个国家列表
        self.target_countries = [
            'BFA', 'COG', 'GRL', 'HKG', 'KNA', 'KOS', 'LCA', 'MAC',
            'NRU', 'PSE', 'SSD', 'SUR', 'TLS', 'TON', 'TWN', 'WSM'
        ]

        # 相似性匹配参数
        self.num_reference_countries = 8  # 每个目标国家选择的参考国家数量
        self.min_similarity_threshold = 0.1  # 最低相似度阈值

        # 时区处理参数
        self.enable_timezone_adjustment = True  # 是否启用时区调整
        self.country_timezones = self._initialize_timezone_data()  # 国家时区数据

        # 数据存储
        self.load_data = None
        self.gdp_data = None
        self.pop_data = None
        self.improved_data = None
        self.similarity_results = {}  # 存储每个国家的相似性匹配结果
        self.timezone_adjustments = {}  # 存储时区调整信息

    def _initialize_timezone_data(self):
        """初始化国家时区数据（相对于UTC的小时偏移）"""
        return {
            # 目标国家
            'BFA': 0,    # 布基纳法索 (UTC+0)
            'COG': 1,    # 刚果共和国 (UTC+1)
            'GRL': -3,   # 格陵兰 (UTC-3, 主要居住区)
            'HKG': 8,    # 香港 (UTC+8)
            'KNA': -4,   # 圣基茨和尼维斯 (UTC-4)
            'KOS': 1,    # 科索沃 (UTC+1)
            'LCA': -4,   # 圣卢西亚 (UTC-4)
            'MAC': 8,    # 澳门 (UTC+8)
            'NRU': 12,   # 瑙鲁 (UTC+12)
            'PSE': 2,    # 巴勒斯坦 (UTC+2)
            'SSD': 2,    # 南苏丹 (UTC+2)
            'SUR': -3,   # 苏里南 (UTC-3)
            'TLS': 9,    # 东帝汶 (UTC+9)
            'TON': 13,   # 汤加 (UTC+13)
            'TWN': 8,    # 台湾 (UTC+8)
            'WSM': 13,   # 萨摩亚 (UTC+13)

            # 主要参考国家
            'USA': -6,   # 美国中部时间
            'CHN': 8,    # 中国 (UTC+8)
            'DEU': 1,    # 德国 (UTC+1)
            'JPN': 9,    # 日本 (UTC+9)
            'GBR': 0,    # 英国 (UTC+0)
            'FRA': 1,    # 法国 (UTC+1)
            'ITA': 1,    # 意大利 (UTC+1)
            'ESP': 1,    # 西班牙 (UTC+1)
            'CAN': -5,   # 加拿大东部时间
            'AUS': 10,   # 澳大利亚东部时间
            'BRA': -3,   # 巴西 (UTC-3)
            'IND': 5.5,  # 印度 (UTC+5:30)
            'RUS': 3,    # 俄罗斯莫斯科时间
            'KOR': 9,    # 韩国 (UTC+9)
            'MEX': -6,   # 墨西哥中部时间
            'TUR': 3,    # 土耳其 (UTC+3)
            'SAU': 3,    # 沙特阿拉伯 (UTC+3)
            'NLD': 1,    # 荷兰 (UTC+1)
            'BEL': 1,    # 比利时 (UTC+1)
            'AUT': 1,    # 奥地利 (UTC+1)
            'DNK': 1,    # 丹麦 (UTC+1)
            'SWE': 1,    # 瑞典 (UTC+1)
            'NOR': 1,    # 挪威 (UTC+1)
            'FIN': 2,    # 芬兰 (UTC+2)
            'POL': 1,    # 波兰 (UTC+1)
            'CZE': 1,    # 捷克 (UTC+1)
            'HUN': 1,    # 匈牙利 (UTC+1)
            'GRC': 2,    # 希腊 (UTC+2)
            'PRT': 0,    # 葡萄牙 (UTC+0)
            'IRL': 0,    # 爱尔兰 (UTC+0)
            'CHE': 1,    # 瑞士 (UTC+1)
            'LUX': 1,    # 卢森堡 (UTC+1)
            'ISL': 0,    # 冰岛 (UTC+0)
            'MLT': 1,    # 马耳他 (UTC+1)
            'CYP': 2,    # 塞浦路斯 (UTC+2)
            'SVN': 1,    # 斯洛文尼亚 (UTC+1)
            'SVK': 1,    # 斯洛伐克 (UTC+1)
            'EST': 2,    # 爱沙尼亚 (UTC+2)
            'LVA': 2,    # 拉脱维亚 (UTC+2)
            'LTU': 2,    # 立陶宛 (UTC+2)
            'BGR': 2,    # 保加利亚 (UTC+2)
            'ROU': 2,    # 罗马尼亚 (UTC+2)
            'HRV': 1,    # 克罗地亚 (UTC+1)
            'SRB': 1,    # 塞尔维亚 (UTC+1)
            'BIH': 1,    # 波黑 (UTC+1)
            'MNE': 1,    # 黑山 (UTC+1)
            'MKD': 1,    # 北马其顿 (UTC+1)
            'ALB': 1,    # 阿尔巴尼亚 (UTC+1)
            'UKR': 2,    # 乌克兰 (UTC+2)
            'BLR': 3,    # 白俄罗斯 (UTC+3)
            'MDA': 2,    # 摩尔多瓦 (UTC+2)
            'ARM': 4,    # 亚美尼亚 (UTC+4)
            'GEO': 4,    # 格鲁吉亚 (UTC+4)
            'AZE': 4,    # 阿塞拜疆 (UTC+4)
            'KAZ': 6,    # 哈萨克斯坦 (UTC+6)
            'UZB': 5,    # 乌兹别克斯坦 (UTC+5)
            'TKM': 5,    # 土库曼斯坦 (UTC+5)
            'KGZ': 6,    # 吉尔吉斯斯坦 (UTC+6)
            'TJK': 5,    # 塔吉克斯坦 (UTC+5)
            'AFG': 4.5,  # 阿富汗 (UTC+4:30)
            'PAK': 5,    # 巴基斯坦 (UTC+5)
            'BGD': 6,    # 孟加拉国 (UTC+6)
            'LKA': 5.5,  # 斯里兰卡 (UTC+5:30)
            'NPL': 5.75, # 尼泊尔 (UTC+5:45)
            'BTN': 6,    # 不丹 (UTC+6)
            'MMR': 6.5,  # 缅甸 (UTC+6:30)
            'THA': 7,    # 泰国 (UTC+7)
            'LAO': 7,    # 老挝 (UTC+7)
            'KHM': 7,    # 柬埔寨 (UTC+7)
            'VNM': 7,    # 越南 (UTC+7)
            'MYS': 8,    # 马来西亚 (UTC+8)
            'SGP': 8,    # 新加坡 (UTC+8)
            'IDN': 7,    # 印度尼西亚西部时间
            'PHL': 8,    # 菲律宾 (UTC+8)
            'BRN': 8,    # 文莱 (UTC+8)
            'MNG': 8,    # 蒙古 (UTC+8)
            'PRK': 9,    # 朝鲜 (UTC+9)
            'NZL': 12,   # 新西兰 (UTC+12)
            'FJI': 12,   # 斐济 (UTC+12)
            'PNG': 10,   # 巴布亚新几内亚 (UTC+10)
            'SLB': 11,   # 所罗门群岛 (UTC+11)
            'VUT': 11,   # 瓦努阿图 (UTC+11)
            'NCL': 11,   # 新喀里多尼亚 (UTC+11)
            'PLW': 9,    # 帕劳 (UTC+9)
            'FSM': 10,   # 密克罗尼西亚 (UTC+10)
            'MHL': 12,   # 马绍尔群岛 (UTC+12)
            'KIR': 12,   # 基里巴斯 (UTC+12)
            'TUV': 12,   # 图瓦卢 (UTC+12)
            'COK': -10,  # 库克群岛 (UTC-10)
            'PYF': -10,  # 法属波利尼西亚 (UTC-10)
            'ASM': -11,  # 美属萨摩亚 (UTC-11)
            'GUM': 10,   # 关岛 (UTC+10)
            'MNP': 10,   # 北马里亚纳群岛 (UTC+10)
            'ZAF': 2,    # 南非 (UTC+2)
            'EGY': 2,    # 埃及 (UTC+2)
            'LBY': 2,    # 利比亚 (UTC+2)
            'TUN': 1,    # 突尼斯 (UTC+1)
            'DZA': 1,    # 阿尔及利亚 (UTC+1)
            'MAR': 1,    # 摩洛哥 (UTC+1)
            'ETH': 3,    # 埃塞俄比亚 (UTC+3)
            'KEN': 3,    # 肯尼亚 (UTC+3)
            'UGA': 3,    # 乌干达 (UTC+3)
            'TZA': 3,    # 坦桑尼亚 (UTC+3)
            'RWA': 2,    # 卢旺达 (UTC+2)
            'BDI': 2,    # 布隆迪 (UTC+2)
            'SOM': 3,    # 索马里 (UTC+3)
            'DJI': 3,    # 吉布提 (UTC+3)
            'ERI': 3,    # 厄立特里亚 (UTC+3)
            'SDN': 2,    # 苏丹 (UTC+2)
            'TCD': 1,    # 乍得 (UTC+1)
            'CAF': 1,    # 中非共和国 (UTC+1)
            'CMR': 1,    # 喀麦隆 (UTC+1)
            'NGA': 1,    # 尼日利亚 (UTC+1)
            'NER': 1,    # 尼日尔 (UTC+1)
            'MLI': 0,    # 马里 (UTC+0)
            'BEN': 1,    # 贝宁 (UTC+1)
            'TGO': 0,    # 多哥 (UTC+0)
            'GHA': 0,    # 加纳 (UTC+0)
            'CIV': 0,    # 科特迪瓦 (UTC+0)
            'LBR': 0,    # 利比里亚 (UTC+0)
            'SLE': 0,    # 塞拉利昂 (UTC+0)
            'GIN': 0,    # 几内亚 (UTC+0)
            'GNB': 0,    # 几内亚比绍 (UTC+0)
            'SEN': 0,    # 塞内加尔 (UTC+0)
            'GMB': 0,    # 冈比亚 (UTC+0)
            'CPV': -1,   # 佛得角 (UTC-1)
            'MRT': 0,    # 毛里塔尼亚 (UTC+0)
            'AGO': 1,    # 安哥拉 (UTC+1)
            'ZMB': 2,    # 赞比亚 (UTC+2)
            'ZWE': 2,    # 津巴布韦 (UTC+2)
            'BWA': 2,    # 博茨瓦纳 (UTC+2)
            'NAM': 2,    # 纳米比亚 (UTC+2)
            'LSO': 2,    # 莱索托 (UTC+2)
            'SWZ': 2,    # 斯威士兰 (UTC+2)
            'MOZ': 2,    # 莫桑比克 (UTC+2)
            'MWI': 2,    # 马拉维 (UTC+2)
            'MDG': 3,    # 马达加斯加 (UTC+3)
            'MUS': 4,    # 毛里求斯 (UTC+4)
            'SYC': 4,    # 塞舌尔 (UTC+4)
            'COM': 3,    # 科摩罗 (UTC+3)
            'MYT': 3,    # 马约特 (UTC+3)
            'REU': 4,    # 留尼汪 (UTC+4)
            'STP': 0,    # 圣多美和普林西比 (UTC+0)
            'GNQ': 1,    # 赤道几内亚 (UTC+1)
            'GAB': 1,    # 加蓬 (UTC+1)
            'COD': 1,    # 刚果民主共和国 (UTC+1)
            'ARG': -3,   # 阿根廷 (UTC-3)
            'CHL': -4,   # 智利 (UTC-4)
            'URY': -3,   # 乌拉圭 (UTC-3)
            'PRY': -3,   # 巴拉圭 (UTC-3)
            'BOL': -4,   # 玻利维亚 (UTC-4)
            'PER': -5,   # 秘鲁 (UTC-5)
            'ECU': -5,   # 厄瓜多尔 (UTC-5)
            'COL': -5,   # 哥伦比亚 (UTC-5)
            'VEN': -4,   # 委内瑞拉 (UTC-4)
            'GUY': -4,   # 圭亚那 (UTC-4)
            'GUF': -3,   # 法属圭亚那 (UTC-3)
            'PAN': -5,   # 巴拿马 (UTC-5)
            'CRI': -6,   # 哥斯达黎加 (UTC-6)
            'NIC': -6,   # 尼加拉瓜 (UTC-6)
            'HND': -6,   # 洪都拉斯 (UTC-6)
            'SLV': -6,   # 萨尔瓦多 (UTC-6)
            'GTM': -6,   # 危地马拉 (UTC-6)
            'BLZ': -6,   # 伯利兹 (UTC-6)
            'CUB': -5,   # 古巴 (UTC-5)
            'JAM': -5,   # 牙买加 (UTC-5)
            'HTI': -5,   # 海地 (UTC-5)
            'DOM': -4,   # 多米尼加共和国 (UTC-4)
            'PRI': -4,   # 波多黎各 (UTC-4)
            'VIR': -4,   # 美属维尔京群岛 (UTC-4)
            'BHS': -5,   # 巴哈马 (UTC-5)
            'TTO': -4,   # 特立尼达和多巴哥 (UTC-4)
            'BRB': -4,   # 巴巴多斯 (UTC-4)
            'VCT': -4,   # 圣文森特和格林纳丁斯 (UTC-4)
            'GRD': -4,   # 格林纳达 (UTC-4)
            'DMA': -4,   # 多米尼克 (UTC-4)
            'ATG': -4,   # 安提瓜和巴布达 (UTC-4)
            'MSR': -4,   # 蒙特塞拉特 (UTC-4)
            'AIA': -4,   # 安圭拉 (UTC-4)
            'VGB': -4,   # 英属维尔京群岛 (UTC-4)
            'TCA': -5,   # 特克斯和凯科斯群岛 (UTC-5)
            'CYM': -5,   # 开曼群岛 (UTC-5)
            'BMU': -4,   # 百慕大 (UTC-4)
            'FRO': 0,    # 法罗群岛 (UTC+0)
            'ISR': 2,    # 以色列 (UTC+2)
            'JOR': 2,    # 约旦 (UTC+2)
            'LBN': 2,    # 黎巴嫩 (UTC+2)
            'SYR': 2,    # 叙利亚 (UTC+2)
            'IRQ': 3,    # 伊拉克 (UTC+3)
            'IRN': 3.5,  # 伊朗 (UTC+3:30)
            'KWT': 3,    # 科威特 (UTC+3)
            'BHR': 3,    # 巴林 (UTC+3)
            'QAT': 3,    # 卡塔尔 (UTC+3)
            'ARE': 4,    # 阿联酋 (UTC+4)
            'OMN': 4,    # 阿曼 (UTC+4)
            'YEM': 3,    # 也门 (UTC+3)
        }

    def apply_timezone_adjustment(self, hourly_pattern, target_timezone, reference_timezone):
        """
        对负荷模式应用时区调整

        参数:
        hourly_pattern: 8760小时的负荷模式
        target_timezone: 目标国家时区
        reference_timezone: 参考国家时区

        返回:
        调整后的负荷模式
        """
        if not self.enable_timezone_adjustment:
            return hourly_pattern

        # 计算时区偏移
        timezone_offset_hours = target_timezone - reference_timezone

        # 处理跨日期线情况
        if timezone_offset_hours > 12:
            timezone_offset_hours -= 24
        elif timezone_offset_hours < -12:
            timezone_offset_hours += 24

        # 如果没有时区差异，直接返回
        if abs(timezone_offset_hours) < 0.1:  # 考虑浮点数精度
            return hourly_pattern

        # 将8760小时数据重塑为365天×24小时
        daily_patterns = hourly_pattern.reshape(365, 24)

        # 对每天的24小时模式进行循环偏移
        # 注意：正偏移表示目标国家比参考国家晚，需要向前偏移模式
        shift_hours = int(round(timezone_offset_hours))
        shifted_patterns = np.roll(daily_patterns, -shift_hours, axis=1)

        # 重新展平为8760小时
        adjusted_pattern = shifted_patterns.flatten()

        return adjusted_pattern

    def get_country_timezone(self, country_code):
        """获取国家的时区信息"""
        return self.country_timezones.get(country_code, 0)  # 默认UTC+0

    def load_all_data(self):
        """加载所有数据文件"""
        print("正在加载所有数据文件...")

        # 加载负荷数据
        try:
            self.load_data = pd.read_excel(self.input_file, engine='openpyxl', sheet_name=0)
            print(f"✓ 成功加载负荷数据，形状: {self.load_data.shape}")
        except Exception as e:
            print(f"✗ 加载负荷数据失败: {e}")
            return False

        # 加载GDP数据
        try:
            gdp_raw = pd.read_csv(self.gdp_file, skiprows=3)
            gdp_raw.columns = ['API', 'Country'] + [str(year) for year in range(1980, 2025)]
            self.gdp_data = self._process_auxiliary_data(gdp_raw, 'INTL.4701-34-', '2023')
            print(f"✓ 成功加载GDP数据，包含 {len(self.gdp_data)} 个国家")
        except Exception as e:
            print(f"✗ 加载GDP数据失败: {e}")
            return False

        # 加载人口数据
        try:
            pop_raw = pd.read_csv(self.pop_file, skiprows=3)
            pop_raw.columns = ['API', 'Country'] + [str(year) for year in range(1980, 2025)]
            self.pop_data = self._process_auxiliary_data(pop_raw, 'INTL.4702-33-', '2023')
            print(f"✓ 成功加载人口数据，包含 {len(self.pop_data)} 个国家")
        except Exception as e:
            print(f"✗ 加载人口数据失败: {e}")
            return False

        return True

    def _process_auxiliary_data(self, raw_data, api_prefix, target_year):
        """处理辅助数据（GDP或人口）"""
        processed_data = {}

        for _, row in raw_data.iterrows():
            api = row['API']
            if pd.notna(api) and api_prefix in str(api):
                # 提取国家代码
                country_code = str(api).split('-')[2]

                # 获取目标年份的数据
                if target_year in raw_data.columns:
                    value = row[target_year]
                    if pd.notna(value) and str(value) != 'NA' and str(value) != '':
                        try:
                            processed_data[country_code] = float(value)
                        except (ValueError, TypeError):
                            continue

        return processed_data
    
    def calculate_similarity(self, target_country):
        """计算目标国家与所有候选参考国家的相似性"""
        # 获取目标国家的指标
        target_indicators = self._get_country_indicators(target_country)
        if target_indicators is None:
            print(f"  ✗ {target_country} 缺少必要的经济指标数据")
            return None

        # 获取所有候选参考国家
        candidate_countries = self._get_candidate_countries()
        if len(candidate_countries) == 0:
            print(f"  ✗ 没有找到有效的候选参考国家")
            return None

        # 计算相似性
        similarities = []
        all_indicators = []
        country_codes = []

        # 收集所有国家的指标
        for country in candidate_countries:
            indicators = self._get_country_indicators(country)
            if indicators is not None:
                all_indicators.append(indicators)
                country_codes.append(country)

        if len(all_indicators) == 0:
            return None

        # 标准化指标
        all_indicators = np.array(all_indicators)
        target_indicators = np.array(target_indicators).reshape(1, -1)

        scaler = StandardScaler()
        all_indicators_scaled = scaler.fit_transform(all_indicators)
        target_indicators_scaled = scaler.transform(target_indicators)

        # 计算欧氏距离并转换为相似度
        distances = euclidean_distances(target_indicators_scaled, all_indicators_scaled)[0]
        similarities = 1 / (1 + distances)

        # 创建相似性结果
        similarity_results = list(zip(country_codes, similarities))
        similarity_results.sort(key=lambda x: x[1], reverse=True)  # 按相似度降序排列

        return similarity_results

    def _get_country_indicators(self, country_code):
        """获取国家的经济社会指标"""
        # 检查数据可用性
        if country_code not in self.gdp_data or country_code not in self.pop_data:
            return None

        gdp = self.gdp_data[country_code]
        population = self.pop_data[country_code]

        # 计算指标
        gdp_per_capita = gdp / population if population > 0 else 0

        # 获取负荷密度（如果有负荷数据）
        load_density = 0
        if country_code in self.load_data.columns:
            annual_load = self.load_data[country_code].sum()
            load_density = annual_load / population if population > 0 else 0

        return [
            np.log(gdp + 1),  # 对数GDP（避免极值影响）
            np.log(population + 1),  # 对数人口
            np.log(gdp_per_capita + 1),  # 对数人均GDP
            np.log(load_density + 1)  # 对数负荷密度
        ]

    def _get_candidate_countries(self):
        """获取候选参考国家列表"""
        candidates = []

        for country in self.load_data.columns[1:]:  # 排除Hours列
            if country not in self.target_countries:  # 排除目标国家
                # 检查负荷数据质量
                country_data = self.load_data[country].dropna()
                if len(country_data) == 8760 and country_data.std() > 0:
                    # 检查变异系数是否合理
                    cv = country_data.std() / country_data.mean()
                    if cv > 0.05:  # 变异系数大于5%
                        candidates.append(country)

        return candidates
    
    def generate_similarity_based_curve(self, annual_total, country_code):
        """
        基于相似性匹配和时区调整为单个国家生成改进的负荷曲线

        参数:
        annual_total: 年度负荷总量
        country_code: 国家代码

        返回:
        改进的8760小时负荷数据
        """
        # 计算相似性并选择参考国家
        similarity_results = self.calculate_similarity(country_code)

        if similarity_results is None:
            # 如果无法计算相似性，使用默认方法
            print(f"  ⚠ {country_code} 使用默认参考模式")
            return self._generate_default_curve(annual_total, country_code)

        # 选择最相似的参考国家
        selected_references = similarity_results[:self.num_reference_countries]

        # 过滤掉相似度过低的国家
        selected_references = [(country, sim) for country, sim in selected_references
                             if sim >= self.min_similarity_threshold]

        if len(selected_references) == 0:
            print(f"  ⚠ {country_code} 没有足够相似的参考国家，使用默认方法")
            return self._generate_default_curve(annual_total, country_code)

        # 存储相似性结果用于报告
        self.similarity_results[country_code] = selected_references

        # 获取目标国家时区
        target_timezone = self.get_country_timezone(country_code)

        # 计算加权平均模式（包含时区调整）
        weighted_pattern = self._calculate_weighted_pattern_with_timezone(
            selected_references, target_timezone, country_code)

        # 添加个性化随机变化
        personalized_pattern = self._add_personalized_variation(weighted_pattern, country_code)

        # 缩放到目标年度总量
        current_sum = np.sum(personalized_pattern)
        scaled_pattern = personalized_pattern * (annual_total / current_sum)

        return scaled_pattern

    def _calculate_weighted_pattern(self, selected_references):
        """计算基于相似度权重的负荷模式"""
        # 提取权重
        countries, similarities = zip(*selected_references)
        weights = np.array(similarities)
        weights = weights / np.sum(weights)  # 归一化权重

        # 收集并标准化参考国家的负荷数据
        patterns = []
        for country in countries:
            if country in self.load_data.columns:
                country_data = self.load_data[country].dropna()
                if len(country_data) == 8760:
                    # 标准化到0-1范围
                    normalized = (country_data - country_data.min()) / (country_data.max() - country_data.min())
                    patterns.append(normalized.values)

        if len(patterns) == 0:
            raise ValueError("没有有效的参考国家负荷数据")

        # 计算加权平均
        patterns_array = np.array(patterns)
        weighted_pattern = np.average(patterns_array, axis=0, weights=weights[:len(patterns)])

        return weighted_pattern

    def _calculate_weighted_pattern_with_timezone(self, selected_references, target_timezone, target_country):
        """计算基于相似度权重和时区调整的负荷模式"""
        # 提取权重
        countries, similarities = zip(*selected_references)
        weights = np.array(similarities)
        weights = weights / np.sum(weights)  # 归一化权重

        # 收集并处理参考国家的负荷数据
        patterns = []
        timezone_info = []

        for country in countries:
            if country in self.load_data.columns:
                country_data = self.load_data[country].dropna()
                if len(country_data) == 8760:
                    # 标准化到0-1范围
                    normalized = (country_data - country_data.min()) / (country_data.max() - country_data.min())

                    # 应用时区调整
                    reference_timezone = self.get_country_timezone(country)
                    adjusted_pattern = self.apply_timezone_adjustment(
                        normalized.values, target_timezone, reference_timezone)

                    patterns.append(adjusted_pattern)

                    # 记录时区调整信息
                    timezone_offset = target_timezone - reference_timezone
                    timezone_info.append((country, reference_timezone, timezone_offset))

        if len(patterns) == 0:
            raise ValueError("没有有效的参考国家负荷数据")

        # 存储时区调整信息
        self.timezone_adjustments[target_country] = timezone_info

        # 计算加权平均
        patterns_array = np.array(patterns)
        weighted_pattern = np.average(patterns_array, axis=0, weights=weights[:len(patterns)])

        return weighted_pattern

    def _add_personalized_variation(self, base_pattern, country_code):
        """添加个性化随机变化"""
        # 为每个国家设置固定种子，确保可重复性
        np.random.seed(hash(country_code) % 2**32)

        # 生成平滑的随机变化
        random_variation = np.random.normal(0, 0.1, 8760)
        # 应用移动平均平滑随机变化
        window_size = 24  # 24小时窗口
        smoothed_variation = np.convolve(random_variation, np.ones(window_size)/window_size, mode='same')

        # 将随机变化添加到基础模式
        variation_strength = 0.15  # 15%的随机变化强度
        modified_pattern = base_pattern + smoothed_variation * variation_strength

        # 确保所有值为正
        modified_pattern = np.maximum(modified_pattern, 0.1)

        return modified_pattern

    def _generate_default_curve(self, annual_total, country_code):
        """生成默认负荷曲线（当无法进行相似性匹配时使用）"""
        # 使用简单的典型日负荷模式
        typical_daily_pattern = np.array([
            0.6, 0.55, 0.5, 0.48, 0.5, 0.55, 0.65, 0.75, 0.85, 0.9, 0.95, 1.0,
            1.0, 0.98, 0.95, 0.98, 1.05, 1.15, 1.25, 1.3, 1.2, 1.0, 0.8, 0.7
        ])

        # 重复365天
        annual_pattern = np.tile(typical_daily_pattern, 365)

        # 添加随机变化
        np.random.seed(hash(country_code) % 2**32)
        random_variation = np.random.normal(0, 0.1, 8760)
        smoothed_variation = np.convolve(random_variation, np.ones(24)/24, mode='same')

        modified_pattern = annual_pattern + smoothed_variation * 0.2
        modified_pattern = np.maximum(modified_pattern, 0.1)

        # 缩放到目标总量
        current_sum = np.sum(modified_pattern)
        scaled_pattern = modified_pattern * (annual_total / current_sum)

        return scaled_pattern
    
    def improve_data(self):
        """改进所有目标国家的数据"""
        print("\n正在改进目标国家的负荷数据（包含时区调整）...")

        # 复制原始数据
        self.improved_data = self.load_data.copy()

        improvement_stats = []

        for i, country in enumerate(self.target_countries):
            if country in self.load_data.columns:
                print(f"正在处理 {country} ({i+1}/{len(self.target_countries)})...")

                # 获取原始年度总量
                original_data = self.load_data[country].dropna()
                annual_total = original_data.sum()
                original_cv = original_data.std() / original_data.mean() if original_data.mean() > 0 else 0

                # 生成改进的负荷曲线
                improved_curve = self.generate_similarity_based_curve(annual_total, country)

                # 更新数据
                self.improved_data[country] = improved_curve

                # 验证总量保持不变
                new_total = improved_curve.sum()
                total_error = abs(new_total - annual_total) / annual_total * 100

                # 计算新的变异系数
                new_cv = np.std(improved_curve) / np.mean(improved_curve)

                # 获取相似性信息
                similarity_info = ""
                if country in self.similarity_results:
                    top_refs = self.similarity_results[country][:3]  # 显示前3个最相似的国家
                    similarity_info = ", ".join([f"{ref}({sim:.3f})" for ref, sim in top_refs])

                # 获取时区信息
                target_timezone = self.get_country_timezone(country)
                timezone_info = f"UTC{target_timezone:+.1f}"

                improvement_stats.append({
                    'country': country,
                    'original_cv': original_cv,
                    'new_cv': new_cv,
                    'total_error_pct': total_error,
                    'annual_total': annual_total,
                    'similarity_info': similarity_info,
                    'timezone': timezone_info
                })

                print(f"  ✓ 原始变异系数: {original_cv:.6f}")
                print(f"  ✓ 新变异系数: {new_cv:.6f}")
                print(f"  ✓ 总量误差: {total_error:.6f}%")
                print(f"  ✓ 时区: {timezone_info}")
                if similarity_info:
                    print(f"  ✓ 主要参考国家: {similarity_info}")
            else:
                print(f"  ✗ 未找到国家 {country} 的数据")

        return improvement_stats
    
    def save_improved_data(self):
        """保存改进后的数据"""
        print(f"\n正在保存改进后的数据到 {self.output_file}...")
        try:
            self.improved_data.to_excel(self.output_file, index=False, engine='openpyxl')
            print(f"✓ 成功保存改进后的数据")
            return True
        except Exception as e:
            print(f"✗ 保存数据失败: {e}")
            return False
    
    def print_summary_statistics(self, improvement_stats):
        """打印改进效果统计摘要"""
        print("\n" + "="*90)
        print("改进效果统计摘要（基于相似性匹配 + 时区调整）")
        print("="*90)

        if not improvement_stats:
            print("没有改进任何国家的数据")
            return

        # 计算统计信息
        original_cvs = [stat['original_cv'] for stat in improvement_stats]
        new_cvs = [stat['new_cv'] for stat in improvement_stats]
        total_errors = [stat['total_error_pct'] for stat in improvement_stats]

        print(f"改进国家数量: {len(improvement_stats)}")
        print(f"平均原始变异系数: {np.mean(original_cvs):.6f}")
        print(f"平均新变异系数: {np.mean(new_cvs):.6f}")
        print(f"变异系数改进倍数: {np.mean(new_cvs)/np.mean(original_cvs) if np.mean(original_cvs) > 0 else float('inf'):.1f}x")
        print(f"平均总量误差: {np.mean(total_errors):.6f}%")
        print(f"最大总量误差: {np.max(total_errors):.6f}%")
        print(f"时区调整功能: {'启用' if self.enable_timezone_adjustment else '禁用'}")

        print("\n各国详细统计:")
        print("-" * 90)
        print(f"{'国家':<6} {'原始CV':<10} {'新CV':<10} {'总量误差%':<12} {'时区':<8} {'主要参考国家':<25}")
        print("-" * 90)

        for stat in improvement_stats:
            similarity_info = stat.get('similarity_info', 'N/A')[:23]  # 限制长度
            timezone_info = stat.get('timezone', 'N/A')
            print(f"{stat['country']:<6} {stat['original_cv']:<10.6f} {stat['new_cv']:<10.6f} "
                  f"{stat['total_error_pct']:<12.6f} {timezone_info:<8} {similarity_info:<25}")

        print("="*90)

        # 打印相似性匹配和时区调整详情
        print("\n相似性匹配和时区调整详情:")
        print("-" * 90)
        for stat in improvement_stats:
            country = stat['country']
            timezone_info = stat.get('timezone', 'N/A')
            print(f"\n{country} (时区: {timezone_info}):")

            if country in self.similarity_results:
                print(f"  参考国家（按相似度排序）:")
                for i, (ref_country, similarity) in enumerate(self.similarity_results[country][:5]):
                    ref_timezone = self.get_country_timezone(ref_country)
                    ref_timezone_str = f"UTC{ref_timezone:+.1f}"
                    print(f"    {i+1}. {ref_country} (时区: {ref_timezone_str}): 相似度 {similarity:.4f}")
            else:
                print(f"  使用默认模式（无相似性数据）")

            # 显示时区调整信息
            if country in self.timezone_adjustments:
                print(f"  时区调整详情:")
                for ref_country, ref_timezone, offset in self.timezone_adjustments[country]:
                    print(f"    {ref_country}: 从UTC{ref_timezone:+.1f}调整到UTC{self.get_country_timezone(country):+.1f} (偏移{offset:+.1f}小时)")

        print("="*80)
    
    def run(self):
        """运行完整的改进流程"""
        print("开始全球电力负荷时序数据改进流程（基于相似性匹配 + 时区调整）")
        print("="*90)

        # 1. 加载所有数据
        if not self.load_all_data():
            return False

        # 2. 改进数据
        improvement_stats = self.improve_data()

        # 3. 保存数据
        if not self.save_improved_data():
            return False

        # 4. 打印统计摘要
        self.print_summary_statistics(improvement_stats)

        print(f"\n✓ 改进流程完成！输出文件: {self.output_file}")
        return True


def main():
    """主函数"""
    print(f"脚本开始执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查输入文件是否存在
    input_file = 'Load_TimeSeries_2023.xlsx'
    if not os.path.exists(input_file):
        print(f"✗ 输入文件 {input_file} 不存在")
        return
    
    # 创建改进器并运行
    improver = LoadDataImprover(input_file, 'Load_TimeSeries_2023_Improved.xlsx')
    
    try:
        success = improver.run()
        if success:
            print("\n🎉 负荷数据改进成功完成！")
        else:
            print("\n❌ 负荷数据改进过程中出现错误")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n脚本结束执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == "__main__":
    main()
