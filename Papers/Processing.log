# 来源小红书笔记 
https://www.xiaohongshu.com/explore/68677ce30000000013013c92?app_platform=ios&app_version=8.93&share_from_user_hidden=true&xsec_source=app_share&type=normal&xsec_token=CByeRuNBCcme4flHOnlUik9RSX8W_vYDJgLeGu2Na9j0A=&author_share=1&xhsshare=WeixinSession&shareRedId=N0pIOEVKOTk2NzUyOTgwNjY0OTc1O0hN&apptime=1753497241&share_id=cf3e8a767c8e44ad972a71c65c74729b&wechatWid=7f9b86a6ebf9f20b3faababbfd803714&wechatOrigin=menu

(base) grinder70@grinder70deMacBook-Air Global_Power_System_Planning % pip install bib4llm
bib4llm convert Papers/EI.bib    
Collecting bib4llm
  Downloading bib4llm-0.2.2-py3-none-any.whl.metadata (6.2 kB)
Collecting bibtexparser (from bib4llm)
  Downloading bibtexparser-1.4.3.tar.gz (55 kB)
  Preparing metadata (setup.py) ... done
Collecting pymupdf4llm (from bib4llm)
  Downloading pymupdf4llm-0.0.27-py3-none-any.whl.metadata (4.8 kB)
Requirement already satisfied: tqdm in /opt/anaconda3/lib/python3.12/site-packages (from bib4llm) (4.66.5)
Requirement already satisfied: watchdog in /opt/anaconda3/lib/python3.12/site-packages (from bib4llm) (4.0.1)
Requirement already satisfied: pyparsing>=2.0.3 in /opt/anaconda3/lib/python3.12/site-packages (from bibtexparser->bib4llm) (3.1.2)
Collecting pymupdf>=1.26.3 (from pymupdf4llm->bib4llm)
  Using cached pymupdf-1.26.3-cp39-abi3-macosx_11_0_arm64.whl.metadata (3.4 kB)
Downloading bib4llm-0.2.2-py3-none-any.whl (29 kB)
Downloading pymupdf4llm-0.0.27-py3-none-any.whl (30 kB)
Downloading pymupdf-1.26.3-cp39-abi3-macosx_11_0_arm64.whl (22.4 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 22.4/22.4 MB 3.5 MB/s eta 0:00:00
Building wheels for collected packages: bibtexparser
  Building wheel for bibtexparser (setup.py) ... done
  Created wheel for bibtexparser: filename=bibtexparser-1.4.3-py3-none-any.whl size=43550 sha256=972968b8ee431acca74812865d4a3e0cf327844cbaeb595f6cec4a68a576e330
  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/1f/7d/e9/1ff2509f13767a55df1279744adfb757f4ab94b2cbe761f56a
Successfully built bibtexparser
Installing collected packages: pymupdf, bibtexparser, pymupdf4llm, bib4llm
Successfully installed bib4llm-0.2.2 bibtexparser-1.4.3 pymupdf-1.26.3 pymupdf4llm-0.0.27
2025-07-26 10:38:32,321 - INFO - Initialized BibliographyProcessor for /Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI.bib
2025-07-26 10:38:32,321 - INFO - Output directory: /Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm
2025-07-26 10:38:32,977 - INFO - Found 87 entries to process
Processing library:   1%|▊                                                                 | 1/87 [00:21<31:22, 21.88s/entry]No files found for entry davidsonSimulatingInstitutionalHeterogeneity2024
No files found for entry douNearrealtimeGlobalGridded2022
Unsupported file type for /Users/<USER>/Zotero/storage/MXL4NM5Q/41560_2018_133_MOESM2_ESM.xlsx
Processing library:  10%|██████▊                                                           | 9/87 [00:44<04:40,  3.60s/entry]No files found for entry guoGridIntegrationFeasibility2023
No files found for entry helvestonQuantifyingCostSavings2022
No files found for entry liuClimateChangeImpacts2023
Processing library:  16%|██████████▍                                                      | 14/87 [01:22<06:41,  5.49s/entry]No files found for entry pengHeterogeneousEffectsBattery2023
No files found for entry PyPSAEarthNewGlobal2023
No files found for entry tehranchiPypsaUsaFlexibleOpenSource2024
No files found for entry tongGeophysicalConstraintsReliability2021
Processing library:  32%|████████████████████▉                                            | 28/87 [03:02<06:28,  6.58s/entry]No files found for entry zhangIntegratedAssessmentChinas
Processing library:  57%|█████████████████████████████████████▎                           | 50/87 [04:14<02:49,  4.59s/entry]No files found for entry zhuoCostIncreaseElectricity2022
Processing library: 100%|█████████████████████████████████████████████████████████████████| 87/87 [08:05<00:00,  5.58s/entry]
2025-07-26 10:46:38,960 - INFO - Processing complete: Processed 76/87 entries successfully (11 failed), 211 MuPDF warnings/errors occurred, 15 referenced files could not be found. Check /Users/<USER>/PycharmProjects/Global_Power_System_Planning/Papers/EI-bib4llm/processing.log for details.
(base) grinder70@grinder70deMacBook-Air Global_Power_System_Planning % 
