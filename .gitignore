# =============================================================================
# 全球电力系统规划项目 .gitignore 配置
# =============================================================================

# Papers目录 - 本地构建的文献知识库
Papers/

# 大型数据文件目录 - Solar-Wind项目的数据文件
Solar-Wind/CF/data/
Solar-Wind/data/gis/

# 大型NetCDF气象数据文件
*.nc

# 大型GIS数据文件
*.shp
*.dbf
*.shx
*.prj
*.cpg

# 压缩的大型数据文件
*.zip
*.rar
*.7z
*.tar.gz
*.tar.bz2

# =============================================================================
# 系统和临时文件
# =============================================================================

# macOS系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
.AppleDouble
.LSOverride

# Windows系统文件
ehthumbs.db
Thumbs.db
Desktop.ini

# =============================================================================
# Python开发环境
# =============================================================================

# Python缓存文件
__pycache__/
*.py[cod]
*$py.class
*.so

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
venv/
env/
ENV/
env.bak/
venv.bak/
.conda/

# =============================================================================
# IDE和编辑器文件
# =============================================================================

# PyCharm
.idea/

# VSCode
.vscode/

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc

# =============================================================================
# Jupyter Notebook
# =============================================================================
.ipynb_checkpoints

# =============================================================================
# 日志和临时文件
# =============================================================================

# 日志文件
*.log

# LaTeX临时文件
*.aux
*.bbl
*.blg
*.fdb_latexmk
*.fls
*.synctex.gz
*.toc

# 临时文件
*.tmp
*.temp
*.bak
~$*

# =============================================================================
# 特定的大文件（如果有其他超过100MB的文件，在这里添加）
# =============================================================================

# 注意：所有.xlsx文件都被保留，因为它们都小于100MB
